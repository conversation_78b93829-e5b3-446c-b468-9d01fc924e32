services:
  api:
    container_name: ghvn-cskh-be
    build:
      context: .
    restart: always
    environment:
      PORT: 3000
      NODE_ENV: "production"
      MONGO_URI: 'mongodb://dev:Xproz2025@mongodb:27017/moleculer-base?authSource=moleculer-base'
      REDIS_URI: 'redis://:a809Vo7H*5i%40@redis:6379/0'
      #MONGO_URI: 'mongodb://mongo/moleculer-base'
      JWT_SECRET: 'jwt-secret-key'
      JWT_REFRESH_SECRET: 'jwt-refresh-secret-key'
      JWT_RESET_PASSWORD_SECRET: 'jwt-reset-password-secret-key'
      JWT_ACTIVATION_SECRET: 'jwt-activation-secret-key'
      DOMAIN: 'http://localhost:3000'
      MAIL_HOST: 'smtp.example.com'
      MAIL_PORT: 587
      MAIL_USER: ''
      MAIL_PASS: ''
      CORS_WHITELIST: 'http://localhost:8080,http://localhost:3000,https://ghvn-admin.xproz.com/*'
    ports:
      - "3000:3000"
    volumes:
      - ./:/app
      - /app/node_modules
    networks:
      - ghvn-net

networks:
  ghvn-net:
    external: true