{"name": "moleculer-base", "version": "1.0.0", "description": "Base Moleculer microservices project with authentication", "scripts": {"dev": "moleculer-runner --hot services/**/*.service.js", "start": "moleculer-runner services", "cli": "moleculer connect NATS", "ci": "jest --watch", "test": "jest --coverage", "lint": "eslint services", "dc:up": "docker-compose up --build -d", "dc:logs": "docker-compose logs -f", "dc:down": "docker-compose down"}, "keywords": ["microservices", "moleculer", "authentication"], "author": "", "devDependencies": {"@babel/eslint-parser": "^7.21.3", "dotenv": "^16.4.5", "eslint": "^8.25.0", "jest": "^27.5.1", "jest-cli": "^27.5.1", "moleculer-repl": "^0.7.4", "prettier": "^3.0.3"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "ajv": "^8.12.0", "bcryptjs": "^2.4.3", "cookie": "^0.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.3", "i18next": "^22.4.11", "i18next-fs-backend": "^2.1.1", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "moleculer": "^0.14.26", "moleculer-db": "^0.8.20", "moleculer-db-adapter-mongoose": "^0.8.13", "moleculer-web": "^0.10.4", "moment": "^2.30.1", "mongoose": "^5.11.15", "mongoose-paginate-v2": "^1.7.1", "nodemailer": "^6.4.17"}, "engines": {"node": ">= 16.x.x"}, "jest": {"coverageDirectory": "../coverage", "testEnvironment": "node", "rootDir": "./services", "roots": ["../test"]}}