"use strict";

const DbMongoose = require("../../mixins/dbMongo.mixin");
const CONVERSATIONS = require("./conversations.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const { MoleculerClientError } = require("moleculer").Errors;
const i18next = require("i18next");

module.exports = {
  name: "conversations",
  mixins: [DbMongoose(CONVERSATIONS), BaseService, FunctionsCommon],

  /**
   * Default settings
   */
  settings: {
    /** REST Basepath */
    rest: "/conversations",

    /** Public fields */
    fields: [
      "_id", "customerId", "customerType", "supportId", "status",
      "closedAt", "tags", "createdBy", "updatedBy", "createdAt", "updatedAt"
    ],

    /** Validator schema for entity */
    entityValidator: {
      customerId: { type: "string" },
      customerType: { type: "string", enum: ["buyer", "shop"] },
      supportId: { type: "string", optional: true },
      status: { type: "string", enum: ["open", "closed"], optional: true },
      tags: { type: "array", items: "string", optional: true }
    },

    /** Populate options */
    populates: {
      customerId: {
        action: "users.get"
      },
      supportId: {
        action: "users.get"
      }
    },
    populateOptions: ["customerId", "supportId"],
  },

  /**
   * Actions
   */
  actions: {
    /**
     * Create a new conversation
     */
    create: {
      rest: "POST /",
      auth: "required",
      params: {
        customerId: { type: "string" },
        customerType: { type: "string", enum: ["buyer", "shop"] },
        supportId: { type: "string", optional: true },
        tags: { type: "array", items: "string", optional: true, default: [] }
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const entity = ctx.params;

        // Check if customer exists
        const customer = await ctx.call("users.get", { id: entity.customerId });
        if (!customer) {
          throw new MoleculerClientError(i18next.t("Customer not found"), 404);
        }

        // Check if support user exists (if provided)
        if (entity.supportId) {
          const support = await ctx.call("users.get", { id: entity.supportId });
          if (!support) {
            throw new MoleculerClientError(i18next.t("Support user not found"), 404);
          }
        }

        // Create conversation
        const conversation = {
          customerId: entity.customerId,
          customerType: entity.customerType,
          supportId: entity.supportId,
          status: "open",
          tags: entity.tags || [],
          createdBy: user._id,
          updatedBy: user._id
        };

        const result = await this.adapter.insert(conversation);

        // Emit event for conversation creation
        this.broker.emit('conversation.created', result);

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Update a conversation
     */
    update: {
      rest: "PUT /:id",
      auth: "required",
      params: {
        id: "string",
        supportId: { type: "string", optional: true },
        tags: { type: "array", items: "string", optional: true }
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id, ...updateData } = ctx.params;
        const updatedBy = user?._id || ctx.params.updatedBy;
        // Check if conversation exists
        const conversation = await this.adapter.findById(id);
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        // Check if support user exists (if provided)
        if (updateData.supportId) {
          const support = await ctx.call("users.get", { id: updateData.supportId });
          if (!support) {
            throw new MoleculerClientError(i18next.t("Support user not found"), 404);
          }
        }

        // Update data
        const update = {
          ...updateData,
          updatedBy
        };

        const result = await this.adapter.updateById(id, update);

        // Emit event for conversation update
        this.broker.emit('conversation.updated', { id, updateData, updatedBy });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Close a conversation
     */
    close: {
      rest: "PUT /:id/close",
      auth: "required",
      params: {
        id: "string"
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id } = ctx.params;

        // Check if conversation exists
        const conversation = await this.adapter.findById(id);
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        if (conversation.status === "closed") {
          throw new MoleculerClientError(i18next.t("Conversation is already closed"), 400);
        }

        // Close conversation
        const result = await this.adapter.updateById(id, {
          $set: {
            status: "closed",
            closedAt: new Date(),
            updatedBy: user._id
          }
        });

        // Emit event for conversation closure
        this.broker.emit('conversation.closed', { id, closedBy: user._id });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Reopen a conversation
     */
    reopen: {
      rest: "PUT /:id/reopen",
      auth: "required",
      params: {
        id: "string"
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id } = ctx.params;

        // Check if conversation exists
        const conversation = await this.adapter.findById(id);
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        if (conversation.status === "open") {
          throw new MoleculerClientError(i18next.t("Conversation is already open"), 400);
        }

        // Reopen conversation
        const result = await this.adapter.updateById(id, {
          $set: {
            status: "open",
            updatedBy: user._id
          },
          $unset: {
            closedAt: 1
          }
        });

        // Emit event for conversation reopening
        this.broker.emit('conversation.reopened', { id, reopenedBy: user._id });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Assign support to a conversation
     */
    assignSupport: {
      rest: "PUT /:id/assign",
      auth: "required",
      params: {
        id: "string",
        supportId: "string"
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id, supportId } = ctx.params;

        // Check if conversation exists
        const conversation = await this.adapter.findById(id);
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        // Check if support user exists
        const support = await ctx.call("users.get", { id: supportId });
        if (!support) {
          throw new MoleculerClientError(i18next.t("Support user not found"), 404);
        }

        // Assign support
        const result = await this.adapter.updateById(id, {
          $set: {
            supportId: supportId,
            updatedBy: user._id
          }
        });

        // Emit event for support assignment
        this.broker.emit('conversation.support.assigned', {
          id,
          supportId,
          assignedBy: user._id
        });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Add tags to a conversation
     */
    addTags: {
      rest: "PUT /:id/tags/add",
      auth: "required",
      params: {
        id: "string",
        tags: { type: "array", items: "string", min: 1 }
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id, tags } = ctx.params;

        // Check if conversation exists
        const conversation = await this.adapter.findById(id);
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        // Add tags (avoid duplicates)
        const result = await this.adapter.updateById(id, {
          $addToSet: { tags: { $each: tags } },
          $set: { updatedBy: user._id }
        });

        // Emit event for tags addition
        this.broker.emit('conversation.tags.added', {
          id,
          tags,
          addedBy: user._id
        });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Remove tags from a conversation
     */
    removeTags: {
      rest: "PUT /:id/tags/remove",
      auth: "required",
      params: {
        id: "string",
        tags: { type: "array", items: "string", min: 1 }
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id, tags } = ctx.params;

        // Check if conversation exists
        const conversation = await this.adapter.findById(id);
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        // Remove tags
        const result = await this.adapter.updateById(id, {
          $pullAll: { tags: tags },
          $set: { updatedBy: user._id }
        });

        // Emit event for tags removal
        this.broker.emit('conversation.tags.removed', {
          id,
          tags,
          removedBy: user._id
        });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Get conversation details
     */
    get: {
      rest: "GET /:id",
      auth: "required",
      params: {
        id: "string"
      },
      async handler(ctx) {
        const { id } = ctx.params;

        // Get conversation
        const conversation = await this.adapter.findById(id);
        if (!conversation) {
          throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
        }

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, conversation);
      }
    },
    /**
     * Get conversation statistics
     */
    getStats: {
      rest: "GET /stats",
      auth: "required",
      cache: {
        keys: [],
        ttl: 60 // 1 minute cache
      },
      async handler(ctx) {
        const stats = await Promise.all([
          // Total conversations
          this.adapter.count({ query: { isDeleted: false } }),

          // Open conversations
          this.adapter.count({ query: { status: "open", isDeleted: false } }),

          // Closed conversations
          this.adapter.count({ query: { status: "closed", isDeleted: false } }),

          // Conversations by customer type
          this.adapter.collection.aggregate([
            { $match: { isDeleted: false } },
            { $group: { _id: "$customerType", count: { $sum: 1 } } }
          ]).toArray(),

          // Conversations by tags
          this.adapter.collection.aggregate([
            { $match: { isDeleted: false, tags: { $exists: true, $ne: [] } } },
            { $unwind: "$tags" },
            { $group: { _id: "$tags", count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 10 }
          ]).toArray()
        ]);

        return {
          total: stats[0],
          open: stats[1],
          closed: stats[2],
          byCustomerType: stats[3],
          topTags: stats[4]
        };
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    /**
     * Find conversations by customer
     */
    async findByCustomer(customerId, options = {}) {
      const query = {
        customerId,
        isDeleted: false,
        ...options.query
      };

      return this.adapter.find({
        query,
        sort: options.sort || ["-updatedAt"],
        limit: options.limit,
        offset: options.offset
      });
    },

    /**
     * Find conversations by support
     */
    async findBySupport(supportId, options = {}) {
      const query = {
        supportId,
        isDeleted: false,
        ...options.query
      };

      return this.adapter.find({
        query,
        sort: options.sort || ["-updatedAt"],
        limit: options.limit,
        offset: options.offset
      });
    }
  },

  /**
   * Events
   */
  events: {
    "user.removed": {
      async handler(ctx) {
        const userId = ctx.params.id;

        // Update conversations where user is customer
        await this.adapter.updateMany(
          { customerId: userId },
          { $set: { isDeleted: true } }
        );

        // Unassign support from conversations
        await this.adapter.updateMany(
          { supportId: userId },
          { $unset: { supportId: 1 } }
        );

        this.logger.info(`User ${userId} removed from conversations`);
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    this.logger.info("Conversations service created");
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    // Create indexes if needed
    if (this.adapter.collection) {
      this.logger.info("Creating indexes for conversations collection...");
      await this.adapter.collection.createIndex({ customerId: 1, status: 1 });
      await this.adapter.collection.createIndex({ supportId: 1, status: 1 });
      await this.adapter.collection.createIndex({ customerType: 1, status: 1 });
      await this.adapter.collection.createIndex({ tags: 1 });
      await this.adapter.collection.createIndex({ isDeleted: 1 });
    }
  }
};
