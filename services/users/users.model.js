const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const {USER, ROLE, FILES} = require('../../constants/dbCollections');
const {encryptPassword} = require("./users.helper");

const {Schema} = mongoose;
const userSchema = new Schema({
  fullName: {
    type: String,
    trim: true,
    required: true,
    "default": ""
  },
  email: {
    type: String,
    trim: true,
    unique: true,
    index: true,
    lowercase: true,
    required: "Please fill in an email"
  },
  password: {
    type: String,
    required: "Please fill in a password"
  },
  gender: {type: String},
  phone: {type: String},
  avatarId: {type: Schema.Types.ObjectId, ref: FILES},
  isDeleted: {type: Boolean, default: false, select: false},
  isSystemAdmin: {type: Boolean, default: false},
  active: {type: Boolean, default: true},
  neverLogin: {type: Boolean, default: true},
  lastLogin: {type: Date},
  lastChangePassword: {type: Date, default: new Date()},
  deviceTokens: [],
  role: {
    type: String,
    enum: ["admin", "user"],
    default: "user"
  },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

userSchema.pre('save', function (next) {
  let user = this;
  // only hash the password if it has been modified (or is new)
  if (!user.isModified('password')) return next();
  user.password = encryptPassword(user.password);
  next();
});

userSchema.plugin(mongoosePaginate);

module.exports = mongoose.model(USER, userSchema, USER);
