"use strict";
const moment = require("moment");
const bcrypt = require('bcryptjs');

const DbMongoose = require("../../mixins/dbMongo.mixin");
const {USER_CODES} = require("../../constants/constant");
const {MoleculerClientError} = require("moleculer").Errors;
const USER = require("./users.model");
const i18next = require("i18next");
const {sendEmail, generateChangePasswordEmail, generateResetPasswordEmail} = require("../../helpers/emailHelper");
const {getConfig} = require("../../config/config");
const {comparePassword, encryptPassword} = require("./users.helper");
const jwt = require("../../helpers/jwt");
const BaseService = require("../../mixins/baseService.mixin");
const config = getConfig(process.env.NODE_ENV);
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const cookie = require("cookie");
const createValidator = require("../../middlewares/validator.middleware");
const {
  createRegisterEmail,
} = require("./emailtemplate/emailtemplate");

module.exports = {
  name: "users",
  mixins: [DbMongoose(USER), BaseService, FunctionsCommon],
  /**
   * Default settings
   */
  settings: {
    /** REST Basepath */
    rest: "/users",
    /** Secret for JWT */
    JWT_SECRET: process.env.JWT_SECRET || "jwt-secret-key",
    JWT_RESET_PASSWORD_SECRET: process.env.JWT_RESET_PASSWORD_SECRET || "jwt-reset-password-secret-key",
    JWT_ACTIVATION_SECRET: process.env.JWT_ACTIVATION_SECRET || "jwt-activation-secret-key",

    /** Public fields */
    fields: [
      "_id", "email", "fullName", "avatarId", "active", "phone", "gender", "isSystemAdmin", "lastLogin", "role"
    ],

    /** Validator schema for entity */
    entityValidator: {
      email: {type: "string", format: "email"},
      fullName: {type: "string", min: 2},
      password: {type: "string", min: 6, optional: true},
    },
    populates: {},
    populateOptions: [],
  },

  actions: {
    /**
     * Login with username & password
     *
     * @actions
     * @param {Object} user - User credentials
     *
     * @returns {Object} Logged in user with token
     */
    login: {
      rest: "POST /login",
      params: {
        $$strict: true,
        email: {type: "string", format: "email"},
        password: {type: "string", min: 1},
      },
      skipToken: true,
      middlewares: [
        // Add validator middleware
        createValidator({
          type: "object",
          required: ["email", "password"],
          properties: {
            email: {type: "string", format: "email"},
            password: {type: "string", minLength: 1}
          },
          additionalProperties: false
        })
      ],
      async handler(ctx) {
        let {email, password} = ctx.params;

        // Add delay to prevent timing attacks
        await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 100));

        const user = await this.adapter.findOne({email: email, isDeleted: false});
        if (!user) {
          throw new MoleculerClientError(i18next.t("login_fail"), 401, "INVALID_CREDENTIALS");
        }

        let authenticated;
        if (user?.password) {
          authenticated = comparePassword(password, user.password);
        }

        if (!authenticated) {
          throw new MoleculerClientError(i18next.t("login_fail"), 401, "INVALID_CREDENTIALS");
        }

        if (!user.active) {
          throw new MoleculerClientError(i18next.t("error_user_login_account_locked"), 403, "ACCOUNT_LOCKED");
        }

        const accessToken = jwt.issue({id: user._id, role: user.role || "user"}, "72h", this.settings.JWT_SECRET);
        const refreshToken = await this.refreshTokenCreator(ctx, user);

        // Set the tokens as cookies in the response
        const cookieOptions = {
          httpOnly: true,
          path: '/',
          maxAge: 30 * 24 * 60 * 60, // 30 day
          sameSite: "none", // Use 'none' for cross-site cookies
          secure: process.env.NODE_ENV === "production" // Use secure cookies in production
        };
        const cookieAccessToken = cookie.serialize("accessToken", accessToken, cookieOptions);
        const cookieRefreshToken = cookie.serialize("refreshToken", refreshToken, cookieOptions);

        ctx.meta.$responseHeaders = {
          "Set-Cookie": [cookieAccessToken, cookieRefreshToken]
        };

        // Update last login
        await this.adapter.updateById(user._id, {lastLogin: new Date(), neverLogin: false});

        // Remove sensitive data
        user.password = undefined;
        delete user.__v;

        const userTransform = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);
        return {user: userTransform};
      },
    },


    findOne: {
      rest: "GET /findOne",
      params: {
        email: {type: "string"},
      },
      auth: "required",
      async handler(ctx) {
        let {email} = ctx.params;
        const user = await this.adapter.findOne({email: email, isDeleted: false});
        if (!user) {
          throw new MoleculerClientError(i18next.t("enter_valid_email"), 422);
        }
        // Remove sensitive data
        user.password = undefined;
        delete user.__v;
        return user;
      }
    },

    forgotPassword: {
      rest: "POST /forgotPassword",
      params: {
        $$strict: true,
        email: {type: "string", format: "email"},
      },
      skipToken: true,
      middlewares: [
        // Add validator middleware
        createValidator({
          type: "object",
          required: ["email"],
          properties: {
            email: {type: "string", format: "email"}
          },
          additionalProperties: false
        })
      ],
      async handler(ctx) {
        let {email} = ctx.params;

        // Add delay to prevent user enumeration attacks
        await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 100));

        const user = await this.adapter.findOne({email: email, isDeleted: false});
        if (!user) {
          // Don't reveal that the email doesn't exist, just return success
          return {success: true, message: i18next.t("check_email_for_reset_link")};
        }

        // Create reset password token that expires in 1 hour
        const resetToken = jwt.issue({id: user._id}, "1h", this.settings.JWT_RESET_PASSWORD_SECRET);
        const resetUrl = `${config.domain}/reset-password?token=${resetToken}`;

        // Send email with reset link
        let mailOptions = {
          from: `${i18next.t("email_user_create_from")} <${config.mail.auth.user}>`,
          to: user.email,
          subject: i18next.t("email_subject_user_forgot_password"),
          html: generateResetPasswordEmail(user.fullName, resetUrl),
        };

        try {
          await sendEmail(mailOptions);
        } catch (error) {
          this.logger.error("Failed to send reset password email:", error);
          // Don't reveal the error to the client
        }

        return {success: true, message: i18next.t("check_email_for_reset_link")};
      },
    },

    register: {
      rest: "POST /register",
      params: {
        $$strict: true, // No additional properties allowed
        email: { type: "string", format: "email" },
        fullName: { type: "string", minLength: 2, maxLength: 100 },
        password: { type: "string", minLength: 6, maxLength: 100 },
        phone: { type: "string", optional: true, pattern: "^[0-9+\\-\\s]{6,20}$" },
        gender: { type: "string", optional: true, enum: ["male", "female", "other"] },
      },
      skipToken: true,
      middlewares: [
        // Add validator middleware
        createValidator({
          type: "object",
          required: ["email", "fullName", "password"],
          properties: {
            email: { type: "string", format: "email" },
            fullName: { type: "string", minLength: 2, maxLength: 100 },
            password: { type: "string", minLength: 6, maxLength: 100 },
            phone: { type: "string", pattern: "^[0-9+\\-\\s]{6,20}$" },
            gender: { type: "string", enum: ["male", "female", "other"] },
          },
          additionalProperties: false
        })
      ],
      async handler(ctx) {
        const data = ctx.params;

        // Check if email already exists
        const checkMail = await this.adapter.findOne({email: data.email});
        if (checkMail) {
          throw new MoleculerClientError(i18next.t("account_already_exists"), 422, "EMAIL_EXISTS");
        }

        // Check if phone already exists (if provided)
        if (data.phone) {
          const checkExistPhone = await this.adapter.findOne({phone: data.phone, isDeleted: false});
          if (checkExistPhone) {
            throw new MoleculerClientError(i18next.t("phone_has_registered"), 422, "PHONE_EXISTS");
          }
        }

        // Create user with default role = "user"
        const userData = {
          ...data,
          role: "user",
          active: true,
          neverLogin: true
        };

        const user = await this.adapter.insert(userData);

        // Generate activation token
        const activationToken = jwt.issue({id: user._id}, "7d", this.settings.JWT_ACTIVATION_SECRET);
        const activateLink = `${config.domain}/activate-account?token=${activationToken}`;

        // Emit event for user registration
        this.broker.emit('user.registered', user);

        // Send welcome email with activation link
        let mailOptions = {
          from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`,
          to: data.email,
          subject: i18next.t('email_user_register_html1'),
          html: createRegisterEmail({account: data.email, fullName: data.fullName}, activateLink),
        };

        sendEmail(mailOptions);

        // Return user without sensitive data
        user.password = undefined;
        delete user.__v;

        return {success: true, user};
      },
    },

    create: {
      rest: "POST /",
      params: {
        email: {type: "string", format: "email"},
        fullName: {type: "string", min: 2},
        password: {type: "string", min: 6, optional: true},
        phone: {type: "string", optional: true},
        gender: {type: "string", optional: true},
        role: {type: "string", optional: true},
        isSystemAdmin: {type: "boolean", optional: true},
      },
      auth: "required",
      async handler(ctx) {
        const data = ctx.params;
        await this.validateEntity(data);

        // Only system admin can create users
        if (!ctx.meta.user || !ctx.meta.user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t("no_permission"), 403);
        }

        // Check if email already exists
        const checkMail = await this.adapter.findOne({email: data.email});
        if (checkMail) {
          throw new MoleculerClientError(i18next.t("user_email_has_registered"));
        }

        // Check if phone already exists (if provided)
        if (data.phone) {
          const checkExistPhone = await this.adapter.findOne({phone: data.phone, isDeleted: false});
          if (checkExistPhone) {
            throw new MoleculerClientError(i18next.t("phone_has_registered"));
          }
        }

        // Generate random password if not provided
        let requiredResetPassword = false;
        if (!data.password) {
          data.password = this.generateRandomPassword();
          requiredResetPassword = true;
        }

        // Create user
        const userData = {
          ...data,
          role: data.role || "user",
          active: true,
          neverLogin: true
        };

        const user = await this.adapter.insert(userData);

        // Generate activation or reset password link
        const activateLink = requiredResetPassword ?
          `${config.domain}/reset-password?token=${jwt.issue({id: user._id}, "7d", this.settings.JWT_RESET_PASSWORD_SECRET)}` :
          `${config.domain}/activate-account?token=${jwt.issue({id: user._id}, "7d", this.settings.JWT_ACTIVATION_SECRET)}`;

        // Emit event for user creation
        this.broker.emit('user.created', user);

        // Send welcome email with activation link
        let mailOptions = {
          from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`,
          to: data.email,
          subject: i18next.t('email_user_register_html1'),
          html: createRegisterEmail({account: data.email, fullName: data.fullName}, activateLink),
        };

        sendEmail(mailOptions);

        // Return user without sensitive data
        user.password = undefined;
        delete user.__v;

        return user;
      },
    },

    changePassword: {
      rest: "POST /changePassword",
      params: {
        oldPassword: {type: "string", min: 6},
        newPassword: {type: "string", min: 6},
      },
      auth: "required",
      async handler(ctx) {
        let {oldPassword, newPassword} = ctx.params;
        let {userID} = ctx.meta;

        // Find user
        const user = await this.adapter.findOne({isDeleted: false, _id: userID});
        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }

        // Verify old password
        const authenticated = comparePassword(oldPassword, user.password);
        if (!authenticated) {
          throw new MoleculerClientError(i18next.t("error_old_password_wrong"), 400);
        }

        // Encrypt new password
        const encryptedPass = encryptPassword(newPassword);

        // Update user
        const userUpdate = await this.adapter.updateById(
          userID,
          {
            password: encryptedPass,
            neverLogin: false,
            lastChangePassword: new Date(),
          },
        );

        // Generate new tokens
        const accessToken = jwt.issue({id: user._id, role: user.role || "user"}, "72h", this.settings.JWT_SECRET);
        const refreshToken = await this.refreshTokenCreator(ctx, user);

        // Set the tokens as cookies in the response
        const cookieOptions = {
          httpOnly: true,
          path: '/',
          maxAge: 30 * 24 * 60 * 60 // 30 day
        };
        const cookieAccessToken = cookie.serialize("accessToken", accessToken, cookieOptions);
        const cookieRefreshToken = cookie.serialize("refreshToken", refreshToken, cookieOptions);

        ctx.meta.$responseHeaders = {
          "Set-Cookie": [cookieAccessToken, cookieRefreshToken]
        };

        // Delete all refresh tokens for this user
        await ctx.call("refreshToken.deleteMany", {userId: user._id, refreshToken: {$ne: refreshToken}});

        // Send email notification
        // const emailContent = generateChangePasswordEmail(userUpdate.fullName, config.domain);
        // let mailOptions = {
        //   from: `${i18next.t("email_user_create_from")} <${config.mail.auth.user}>`,
        //   to: userUpdate.email,
        //   subject: i18next.t("password_changed_successfully"),
        //   html: emailContent,
        // };

        // sendEmail(mailOptions);

        // Return user without sensitive data
        userUpdate.password = undefined;
        delete userUpdate.__v;

        return {success: true, user: userUpdate};
      },
    },

    resetPassword: {
      rest: "POST /resetPassword",
      params: {
        $$strict: true,
        token: {type: "string"},
        password: {type: "string", min: 6, max: 100},
      },
      skipToken: true,
      middlewares: [
        // Add validator middleware
        createValidator({
          type: "object",
          required: ["token", "password"],
          properties: {
            token: {type: "string", minLength: 10},
            password: {type: "string", minLength: 6, maxLength: 100}
          },
          additionalProperties: false
        })
      ],
      async handler(ctx) {
        const {token, password} = ctx.params;

        try {
          // Verify reset password token
          const decoded = await jwt.verifyToken(token, this.settings.JWT_RESET_PASSWORD_SECRET);
          if (!decoded || !decoded.id) {
            throw new MoleculerClientError(i18next.t("invalid_or_expired_token"), 400, "INVALID_TOKEN");
          }

          // Find user
          const user = await this.adapter.findOne({isDeleted: false, _id: decoded.id});
          if (!user) {
            throw new MoleculerClientError(i18next.t("error_user_not_found"), 404, "USER_NOT_FOUND");
          }

          // Check if password is different from the current one
          if (comparePassword(password, user.password)) {
            throw new MoleculerClientError(i18next.t("new_password_must_be_different"), 422, "SAME_PASSWORD");
          }

          // Encrypt new password
          const encryptedPass = encryptPassword(password);

          // Update user
          const userUpdate = await this.adapter.updateById(
            user._id,
            {
              password: encryptedPass,
              neverLogin: false,
              lastChangePassword: new Date(),
            },
            {new: true}
          );

          // Send email notification
          let mailOptions = {
            from: `${i18next.t("email_user_create_from")} <${config.mail.auth.user}>`,
            to: userUpdate.email,
            subject: i18next.t("password_reset_successfully"),
            html: generateChangePasswordEmail(userUpdate.fullName, config.domain),
          };

          try {
            await sendEmail(mailOptions);
          } catch (error) {
            this.logger.error("Failed to send password reset confirmation email:", error);
            // Don't reveal the error to the client
          }

          // Delete all refresh tokens for this user
          await ctx.call("refreshToken.deleteMany", {userId: user._id});

          return {success: true, message: i18next.t('reset_password_successfully')};
        } catch (error) {
          if (error instanceof MoleculerClientError) {
            throw error;
          }
          // For other errors, return a generic message
          throw new MoleculerClientError(i18next.t("invalid_or_expired_token"), 400, "INVALID_TOKEN");
        }
      },
    },

    /**
     * Get user by JWT token (for API GW authentication)
     *
     * @actions
     * @param {String} token - JWT token
     *
     * @returns {Object} Resolved user
     */
    resolveToken: {
      cache: {
        keys: ["accessToken"],
        ttl: 60 * 60, // 1 hour
      },
      params: {
        accessToken: "string",
      },
      async handler(ctx) {
        const decoded = await jwt.verifyToken(
          ctx.params.accessToken,
          this.settings.JWT_SECRET,
        );
        if (decoded?.id) {
          const user = await this.getById(decoded.id);
          if (user && !user.isDeleted && user.active) {
            return this.transformDocuments(ctx, {}, user);
          }
        }
        return null;
      },
    },

    resolveResetPasswordToken: {
      params: {
        token: "string",
      },
      async handler(ctx) {
        const decoded = await jwt.verifyToken(
          ctx.params.token,
          this.settings.JWT_RESET_PASSWORD_SECRET
        );
        if (decoded?.id) {
          const user = await this.getById(decoded.id);
          if (user && !user.isDeleted) {
            return { valid: true, email: user.email };
          }
        }
        return { valid: false };
      },
    },

    me: {
      rest: "GET /me",
      auth: "required",
      async handler(ctx) {
        const user = await this.getById(ctx.meta.userID);
        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }
        const userTransformed = await this.transformDocuments(ctx, {}, user);
        delete userTransformed.password;
        return userTransformed;
      },
    },

    getProfile: {
      rest: "GET /profile",
      auth: "required",
      async handler(ctx) {
        const user = await this.getById(ctx.meta.userID);
        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }
        const userTransformed = await this.transformDocuments(ctx, {}, user);
        delete userTransformed.password;
        return userTransformed;
      },
    },

    updateProfile: {
      rest: "PUT /profile",
      auth: "required",
      params: {
        fullName: {type: "string", min: 2, optional: true},
        phone: {type: "string", optional: true},
        gender: {type: "string", optional: true},
        avatar: {type: "string", optional: true},
      },
      async handler(ctx) {
        const update = ctx.params;
        const user = await this.getById(ctx.meta.userID);
        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }

        // Check if phone already exists (if provided)
        if (update.phone) {
          const checkExistPhone = await this.adapter.findOne({
            phone: update.phone,
            isDeleted: false,
            _id: {$ne: ctx.meta.userID},
          });
          if (checkExistPhone) {
            throw new MoleculerClientError(i18next.t("phone_has_registered"));
          }
        }

        // Update user profile
        const updatedUser = await this.adapter.updateById(ctx.meta.userID, update);
        const userTransformed = await this.transformDocuments(ctx, {}, updatedUser);
        delete userTransformed.password;
        return userTransformed;
      },
    },

    logout: {
      rest: "POST /logout",
      auth: "required",
      async handler(ctx) {
        const {userID, refreshToken} = ctx.meta;

        // Delete refresh token from database
        if (refreshToken) {
          await ctx.call("refreshToken.delete", {refreshToken});
        }

        // Clear cookies
        const cookieOptions = {
          httpOnly: true,
          path: '/',
          maxAge: 0
        };
        const cookieAccessToken = cookie.serialize("accessToken", "", cookieOptions);
        const cookieRefreshToken = cookie.serialize("refreshToken", "", cookieOptions);

        ctx.meta.$responseHeaders = {
          "Set-Cookie": [cookieAccessToken, cookieRefreshToken]
        };

        return {success: true, message: i18next.t("logout_successful")};
      },
    },

    updateInfo: {
      rest: "PATCH /info",
      auth: "required",
      async handler(ctx) {
        const id = ctx.meta.user._id;
        const value = ctx.params;

        delete value.password;
        delete value.role;
        delete value.isSystemAdmin;
        delete value.permissions;
        delete value.active;
        delete value.lastLogin;
        delete value.isDeleted;

        // check unique email
        const checkMail = await this.adapter.findOne({_id: {$ne: id}, email: value.email}, {_id: 1});
        if (checkMail) {
          throw new MoleculerClientError(i18next.t('user_email_has_registered'), 422);
        }
        if (value.phone) {
          const checkExistPhone = await this.adapter.findOne({
            phone: value.phone, isDeleted: false, _id: {$ne: id},
          });
          if (checkExistPhone) {
            throw new MoleculerClientError("Phone number already exists");
          }
        }
        const user = await this.adapter.updateById(id, value, {new: true});

        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }

        if (user.email) {
          let mailOptions = {
            from: `${i18next.t("email_user_create_from")} <${config.mail.auth.user}>`, // sender address
            to: value.email, // list of receivers
            subject: i18next.t("account_information_updated_successfully"), // Subject line
            //text: 'Pass moi la 123455', // plaintext body
            html: `<h2>${i18next.t("account_information")}</h2>
              <div><strong>${i18next.t("full_name")}: </strong>${user.fullName}</div>
              <div><strong>${i18next.t("phone")}: </strong>${user.phone || ""}</div>
              <div><strong>${i18next.t("email")}: </strong>${user.email}</div>
              <div>${i18next.t("sign_in")} <a href="${config.domain}">Link</a></div>`, // html body
          };
          sendEmail(mailOptions, (err) => {
            if (err) {
              console.log(err);
            }
          });
        }
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user,);
      },
    },

    remove: {
      rest: "DELETE /:id",
      auth: "required",
      params: {
        id: {type: "string", min: 3},
      },
      async handler(ctx) {
        const {id} = ctx.params;
        let user = await this.adapter.findById(id);
        if (user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t("error_delete_sysadmin"));
        }

        return await this.adapter.updateById(id, {isDeleted: true});
      },
    },
    update: {
      rest: "PUT /:id",
      auth: "required",
      params: {
        id: {type: "string", min: 3},
      },
      async handler(ctx) {
        const value = ctx.params;

        // check unique email
        const checkMail = await this.adapter.findOne({_id: {$ne: value.id}, email: value.email});
        if (checkMail) {
          throw new MoleculerClientError(i18next.t("user_email_has_registered"), 422);
        }
        if (value.phone) {
          const checkExistPhone = await this.adapter.findOne({
            phone: value.phone, isDeleted: false, _id: {$ne: value.id},
          });
          if (checkExistPhone) {
            throw new MoleculerClientError(i18next.t("phone_has_registered"));
          }
        }
        const user = await this.adapter.updateById(value.id, value, {new: true});

        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }

        if (user.email) {
          let mailOptions = {
            from: `${i18next.t("email_user_create_from")} <${config.mail.auth.user}>`, // sender address
            to: value.email, // list of receivers
            subject: i18next.t("account_information_updated_successfully"), // Subject line
            //text: 'Pass moi la 123455', // plaintext body
            html: `<h2>${i18next.t("account_information")}</h2>
              <div><strong>${i18next.t("full_name")}: </strong>${user.fullName}</div>
              <div><strong>${i18next.t("phone")}: </strong>${user.phone || ""}</div>
              <div><strong>${i18next.t("email")}: </strong>${user.email}</div>
              <div>${i18next.t("sign_in")} <a href="${config.domain}">Link</a></div>`, // html body
          };

          sendEmail(mailOptions, (err) => {
            if (err) {
              console.log(err);
            }
          });
        }
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);
      },
    },

    generateAccessToken: {
      rest: "POST /generateAccessToken",
      async handler(ctx) {
        const {refreshToken} = ctx.meta;
        if (!refreshToken) {
          throw new MoleculerClientError(i18next.t("error_unauthorized"), 401);
        }
        const decoded = await jwt.verifyToken(
          refreshToken,
          this.settings.JWT_SECRET,
        );
        if (decoded?.id) {
          const user = await this.getById(decoded.id);
          const accessToken = jwt.issue({id: user?._id, isUser: true}, "72h", this.settings.JWT_SECRET);
          const cookieOptions = {
            httpOnly: true,
            path: '/',
            maxAge: 30 * 24 * 60 * 60 // 30 day
          };
          const refreshToken = await this.refreshTokenCreator(ctx, user);
          const cookieAccessToken = cookie.serialize("accessToken", accessToken, cookieOptions);
          const cookieRefreshToken = cookie.serialize("refreshToken", refreshToken, cookieOptions);

          ctx.meta.$responseHeaders = {
            "Set-Cookie": [cookieAccessToken, cookieRefreshToken]
          };
        }
      }
    },

    confirmInvitation: {
      rest: "POST /confirmInvitation",
      async handler(ctx) {
        const {user} = ctx.meta;
        const {organizationId} = ctx.params;
        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }
        ctx.emit("userConfirmInvitation", {user, organizationId});
        return await this.adapter.updateById(user._id, {organizationId, role: 'normal'});
      }
    },
    rejectInvitation: {
      rest: "POST /rejectInvitation",
      async handler(ctx) {
        const {user} = ctx.meta;
        const {organizationId} = ctx.params;
        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }
        ctx.emit("userRejectInvitation", {user, organizationId});
        return {success: true, message: i18next.t("Invitation reject success")};
      }
    },

    getOneByEmail: {
      rest: "GET /getOneByEmail",
      async handler(ctx) {
        const {email} = ctx.params;
        console.log(email);
        const user = await this.adapter.findOne({email});
        if (!user) {
          throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
        }
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, user);
      }
    }

  },

  methods: {
    /**
     * Create a refresh token for a user
     * @param {Object} ctx - Context
     * @param {Object} user - User object
     * @returns {String} Refresh token
     */
    async refreshTokenCreator(ctx, user) {
      if (!user) {
        throw new MoleculerClientError(i18next.t("error_user_not_found"), 404);
      }

      // Create refresh token that expires in 30 days
      const refreshToken = jwt.issue({id: user._id, role: user.role || "user"}, "30d", this.settings.JWT_SECRET);

      // Calculate expiration date for database
      const expiresDate = moment().add(30, "days").format("YYYY-MM-DD HH:mm:ss");

      // Store refresh token in database
      await ctx.call("refreshToken.create", {
        userId: user._id,
        refreshToken: refreshToken,
        expiresDate: expiresDate,
      });
      return refreshToken;
    },

    /**
     * Wait for a specified delay
     * @param {Number} delay - Delay in milliseconds
     * @returns {Promise}
     */
    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },

    /**
     * Seed the database with initial data
     */
    async seedDB() {
      this.logger.info("Seeding Users database...");
      // Create admin user
      const admin = {
        email: "<EMAIL>",
        password: "admin123",
        fullName: "System Admin",
        isSystemAdmin: true,
        role: "admin",
        active: true,
      };
      await this.adapter.insert(admin);

      this.logger.info(`Generated admin user!`);
      return this.clearCache();
    },

    /**
     * Compare a plain text password with a hashed password
     * @param {String} plainPassword - Plain text password
     * @param {String} hashedPassword - Hashed password
     * @returns {Promise<Boolean>} Promise that resolves to true if passwords match
     */
    async comparePassword(plainPassword, hashedPassword) {
      return bcrypt.compareSync(plainPassword, hashedPassword);
    },

    /**
     * Generate a random password
     * @returns {String} Random password
     */
    generateRandomPassword() {
      return Math.random().toString(36).slice(-8);
    },
  },

  events: {},

  async afterConnected() {
    const count = await this.adapter.count();
    if (count === 0) {
      return this.seedDB();
    }
  },

};
