"use strict";

const DbMongoose = require("../../mixins/dbMongo.mixin");
const {MoleculerClientError} = require("moleculer").Errors;
const FILES = require("./shop.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const FileMixin = require("../../mixins/file.mixin");
const i18next = require("i18next");

module.exports = {
  name: "shops",
  mixins: [DbMongoose(FILES), BaseService, FunctionsCommon, FileMixin],

  /**
   * Default settings
   */
  settings: {
    /** REST Basepath */
    rest: "/shops",
  },

  /**
   * Actions
   */
  actions: {},

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {},

  /**
   * Service created lifecycle event handler
   */
  created() {
  }
};
