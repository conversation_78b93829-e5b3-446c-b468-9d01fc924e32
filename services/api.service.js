"use strict";

const ApiGateway = require("moleculer-web");
const i18next = require("i18next");
const backend = require("i18next-fs-backend");
const _ = require("lodash");
const {enTranslation} = require("../locales/en/translation.js");
const {viTranslation} = require("../locales/vi/translation.js");
const {MoleculerError} = require("moleculer").Errors;
const createRateLimiter = require("../middlewares/rate-limiter.middleware");
const createCorsMiddleware = require("../middlewares/cors.middleware");

// i18n configuration
const resources = {
  en: {
    translation: enTranslation,
  },
  vi: {
    translation: viTranslation,
  },
};

i18next
  .use(backend)
  .init({
    fallbackLng: "en", // Fallback language
    preload: ["en", "vi"], // Preload all supported languages
    resources: resources,
    ns: ["translation"],
    backend: {
      loadPath: "locales/{{lng}}/{{ns}}.js",
    },
  })
  .then(() => console.log("i18next initialized"));

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 * @typedef {import('http').IncomingMessage} IncomingRequest Incoming HTTP Request
 * @typedef {import('http').ServerResponse} ServerResponse HTTP Server Response
 * @typedef {import('moleculer-web').ApiSettingsSchema} ApiSettingsSchema API Setting Schema
 */
module.exports = {
  name: "api",
  mixins: [ApiGateway],

  /** @type {ApiSettingsSchema} More info about settings: https://moleculer.services/docs/0.14/moleculer-web.html */
  settings: {
    // Exposed port
    port: process.env.PORT || 3000,

    // Exposed IP
    ip: "0.0.0.0",

    // Global Express middlewares. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Middlewares
    use: [
      // CORS middleware
      createCorsMiddleware({
        whitelist: [
          "https://ghvn-admin.xproz.com",
          "http://localhost:3000",
          "http://127.0.0.1:3000",
          "https://ghvn-api.xproz.com",
          "http://localhost:8080",
          "http://127.0.0.1:8080"
        ]
      }),

      createRateLimiter({
        window: 60,           // 1 minute window
        limit: 100,          // 100 requests per minute per IP
        skipPaths: ["/api/health"] // Skip rate limiting for health checks
      })
    ],

    routes: [
      {
        path: "/api",
        cors: false,
        whitelist: ["**"],

        // Route-level Express middlewares. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Middlewares
        use: [],

        // Enable/disable parameter merging method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Disable-merging
        mergeParams: true,

        // Enable authentication. Implement the logic into `authenticate` method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Authentication
        authentication: true,

        // Enable authorization. Implement the logic into `authorize` method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Authorization
        authorization: true,

        // The auto-alias feature allows you to declare your route alias directly in your services.
        // The gateway will dynamically build the full routes from service schema.
        autoAliases: true,

        aliases: {
          // Swagger documentation
          "GET /docs": "$node.swagger",
          "GET /docs/swagger.json": (req, res) => {
            res.setHeader("Content-Type", "application/json");
            res.end(JSON.stringify(require("../public/swagger.json")));
          }
        },

        // Calling options. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Calling-options
        callingOptions: {},

        bodyParsers: {
          json: {
            strict: false,
            limit: "1MB",
          },
          urlencoded: {
            extended: true,
            limit: "1MB",
          },
        },

        // Mapping policy setting. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Mapping-policy
        mappingPolicy: "all", // Available values: "all", "restrict"

        // Enable/disable logging
        logging: true,
        // Route error handler
        onBeforeCall(ctx, route, req, res) {
          const lang = req.headers.i18nextlng || "en";
          if (req.query?.token) {
            req.headers.authorization = `Bearer ${req.query?.token}`;
            delete req.query.token;
          }
          i18next.changeLanguage(lang);
          ctx.meta.lang = lang;
          ctx.meta.client_ip = req.headers["x-forwarded-for"] || req.connection.remoteAddress || req.socket.remoteAddress || req.connection.socket.remoteAddress;
        },
        onError(req, res, err) {
          res.setHeader("Content-Type", "application/json; charset=utf-8");
          res.writeHead(err.statusCode || err.code || 500);
          res.end(JSON.stringify({
            success: false,
            message: err.message || err,
            code: err.code || 500
          }));
        }
      },
      {
        path: "/upload",

        // Enable authentication. Implement the logic into `authenticate` method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Authentication
        authentication: true,

        // Enable authorization. Implement the logic into `authorize` method. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Authorization
        authorization: true,

        // You should disable body parsers
        bodyParsers: {
          json: true,
          urlencoded: false,
        },

        aliases: {
          // File upload from HTML form
          "POST /file": "multipart:files.upload",
        },

        // https://github.com/mscdex/busboy#busboy-methods
        busboyConfig: {
          limits: {
            files: 1,
          },
        },

        callOptions: {
          meta: {},
        },
        mappingPolicy: "restrict",
      }
    ],

    // Do not log client side errors (does not log an error response when the error.code is 400<=X<500)
    log4XXResponses: false,
    // Logging the request parameters. Set to any log level to enable it. E.g. "info"
    logRequestParams: null,
    // Logging the response data. Set to any log level to enable it. E.g. "info"
    logResponseData: null,

    // Serve assets from "public" folder. More info: https://moleculer.services/docs/0.14/moleculer-web.html#Serve-static-files
    assets: {
      folder: "public",

      // Options to `server-static` module
      options: {},
    },
    JWT_SECRET: process.env.JWT_SECRET || "jwt-secret-key",
    // Global error handler
    onError(req, res, err) {
      res.setHeader("Content-Type", "application/json; charset=utf-8");
      res.writeHead(err.statusCode || err.code || 500);
      res.end(JSON.stringify({
        success: false,
        code: err.code,
        message: err.message
      }));
    }
  },

  methods: {
    /**
     * Authenticate the request. It check the `Authorization` token value in the request header.
     * Check the token value & resolve the user by the token.
     * The resolved user will be available in `ctx.meta.user`
     *
     * @param {Context} ctx
     * @param {Object} route
     * @param {IncomingRequest} req
     * @returns {Promise}
     */
    async authenticate(ctx, route, req) {
      // Get token from cookie or authorization header
      let accessToken, refreshToken;

      // 1. Check cookies first
      if (req.headers.cookie) {
        const cookieHeaders = req.headers.cookie.split(";");
        cookieHeaders.forEach((cookieItem) => {
          const [type, token] = cookieItem.trim().split("=");
          if (type === "accessToken") accessToken = token;
          if (type === "refreshToken") refreshToken = token;
        });
      }

      // 2. Check authorization header
      if (!accessToken && req.headers.authorization) {
        let [type, token] = req.headers.authorization.split(" ");
        if (type === "Bearer") accessToken = token;
      }

      // 3. Check URL query parameter (for specific endpoints only)
      const urlCheckBearer = ["resetPassword", "activateAccount"].some(url => req.originalUrl.includes(url));
      if (!accessToken && urlCheckBearer && req.query?.token) {
        accessToken = req.query.token;
      }

      // Store refresh token in context meta
      ctx.meta.refreshToken = refreshToken;

      // If no token found, return null (not authenticated)
      if (!accessToken) {
        return null;
      }

      // Verify JWT accessToken
      try {
        const user = await ctx.call("users.resolveToken", {accessToken});
        if (user) {
          this.logger.info("Authenticated via JWT: ", user.email);

          // Store token and user ID in context meta
          ctx.meta.accessToken = accessToken;
          ctx.meta.userID = user._id;

          // Return user data (will be available in ctx.meta.user)
          return _.pick(user, ["_id", "email", "role", "isSystemAdmin", "fullName"]);
        }
      } catch (err) {
        this.logger.error("JWT authentication error:", err);
        // Token verification failed
        throw new ApiGateway.Errors.UnAuthorizedError("INVALID_TOKEN");
      }

      return null;
    },

    /**
     * Authorize the request. Check that the authenticated user has right to access the resource.
     *
     * @param {Context} ctx
     * @param {Object} route
     * @param {IncomingRequest} req
     * @returns {Promise}
     */
    async authorize(ctx, route, req) {
      // Get the authenticated user
      const user = ctx.meta.user;
      req.user = user;

      // Set language based on request header
      const lang = req.headers.i18nextlng || "en";
      i18next.changeLanguage(lang);

      // Check if authentication is required for this action
      if (req.$action.auth === "required" && !user) {
        throw new ApiGateway.Errors.UnAuthorizedError("UNAUTHORIZED", {
          error: "Authentication is required for this endpoint"
        });
      }

      // Check role-based access if roles are specified in the action
      if (user && req.$action.roles) {
        const roles = Array.isArray(req.$action.roles) ? req.$action.roles : [req.$action.roles];

        // System admin can access everything
        if (user.isSystemAdmin) {
          return;
        }

        // Check if user has the required role
        if (!roles.includes(user.role)) {
          throw new ApiGateway.Errors.ForbiddenError("FORBIDDEN", {
            error: `You need one of these roles: ${roles.join(', ')}`
          });
        }
      }

      // Check owner access if specified in the action
      if (user && req.$action.needsOwnership) {
        // The actual ownership check will be done in the action handler
        // We just set a flag in the context to indicate that ownership check is needed
        ctx.meta.needsOwnership = true;
      }
    }
  }
};
