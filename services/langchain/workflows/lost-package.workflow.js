"use strict";

const {StateGraph, END, START, Annotation} = require("@langchain/langgraph");

// Define the state schema
const WorkflowState = Annotation.Root({
  category: Annotation(),
  question: Annotation(),
  userType: Annotation(),
  businessContext: Annotation(),
  currentStep: Annotation(),
  recommendations: Annotation(),
  workflowData: Annotation(),
  orderCode: Annotation(),
  compensationAmount: Annotation(),
  hasInsurance: Annotation(),
  documentsProvided: Annotation(),
  approvalStatus: Annotation()
});

/**
 * Create Lost Package Compensation Workflow
 * States: verification → documentation → approval → payment
 * @returns {StateGraph} LangGraph workflow for lost package compensation
 */
function createLostPackageWorkflow() {
  const workflow = new StateGraph(WorkflowState);

  // Step 1: Verification
  workflow.addNode("verification", async (state) => {
    const recommendations = [];
    const orderCode = state.businessContext?.orderCode;
    
    if (state.userType === "shop") {
      recommendations.push("🔍 BƯỚC 1: XÁC MINH THÔNG TIN");
      if (orderCode) {
        recommendations.push(`📦 Đơn hàng: ${orderCode} - <PERSON><PERSON>m tra trạng thái thất lạc`);
      }
      recommendations.push("📋 Thu thập thông tin cần thiết:");
      recommendations.push("   • Khai giá hàng hóa");
      recommendations.push("   • Cước phí vận chuyển");
      recommendations.push("   • Có mua bảo hiểm không");
      recommendations.push("📞 Liên hệ CSKH để xác nhận trạng thái");
    } else {
      recommendations.push("🔍 BƯỚC 1: XÁC MINH TRONG HỆ THỐNG");
      if (orderCode) {
        recommendations.push(`📊 Tra cứu đơn ${orderCode}`);
      }
      recommendations.push("✅ Xác nhận đơn hàng bị thất lạc");
      recommendations.push("📋 Kiểm tra thông tin: khai giá, cước phí, bảo hiểm");
      recommendations.push("💰 Tính toán mức đền bù sơ bộ");
    }

    return {
      ...state,
      currentStep: "documentation",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        verificationComplete: true,
        orderCode: orderCode
      }
    };
  });

  // Step 2: Documentation
  workflow.addNode("documentation", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("📄 BƯỚC 2: CHUẨN BỊ CHỨNG TỪ");
      recommendations.push("📸 Cung cấp các tài liệu sau:");
      recommendations.push("   • Ảnh chuyển khoản từ khách hàng");
      recommendations.push("   • Tin nhắn/email đặt hàng");
      recommendations.push("   • Hóa đơn mua hàng (nếu có)");
      recommendations.push("🏦 Thông tin tài khoản ngân hàng:");
      recommendations.push("   • Tên chủ tài khoản");
      recommendations.push("   • Số tài khoản");
      recommendations.push("   • Chi nhánh ngân hàng");
      recommendations.push("📱 Gửi qua chat box hoặc email");
    } else {
      recommendations.push("📄 BƯỚC 2: XỬ LÝ CHỨNG TỪ");
      recommendations.push("📋 Hướng dẫn shop cung cấp:");
      recommendations.push("   • Chứng từ thanh toán");
      recommendations.push("   • Thông tin tài khoản nhận đền bù");
      recommendations.push("🔍 Kiểm tra tính hợp lệ của chứng từ");
      recommendations.push("📝 Tạo hồ sơ đền bù");
      recommendations.push("📤 Chuyển hồ sơ lên bộ phận phê duyệt");
    }

    return {
      ...state,
      currentStep: "approval",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        documentationStep: true
      }
    };
  });

  // Step 3: Approval
  workflow.addNode("approval", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("⏳ BƯỚC 3: CHỜ PHÊ DUYỆT");
      recommendations.push("📋 Hồ sơ đã được gửi lên bộ phận xử lý");
      recommendations.push("⏱️ Thời gian xử lý: 3-5 ngày làm việc");
      recommendations.push("📱 Theo dõi tiến độ qua chat box");
      recommendations.push("📞 Liên hệ CSKH nếu cần hỗ trợ thêm");
    } else {
      recommendations.push("✅ BƯỚC 3: XỬ LÝ PHÊ DUYỆT");
      recommendations.push("📊 Đánh giá hồ sơ đền bù");
      recommendations.push("💰 Xác định mức đền bù cuối cùng:");
      recommendations.push("   • Có bảo hiểm: Theo giá trị khai báo");
      recommendations.push("   • Không bảo hiểm: Tối đa 4x cước phí");
      recommendations.push("📝 Cập nhật kết quả phê duyệt");
      recommendations.push("📞 Thông báo kết quả cho shop");
    }

    return {
      ...state,
      currentStep: "payment",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        approvalStep: true
      }
    };
  });

  // Step 4: Payment
  workflow.addNode("payment", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("💰 BƯỚC 4: NHẬN THANH TOÁN");
      recommendations.push("✅ Đền bù đã được phê duyệt");
      recommendations.push("🏦 Tiền sẽ được chuyển khoản trong 7-10 ngày");
      recommendations.push("📱 Kiểm tra tài khoản thường xuyên");
      recommendations.push("📞 Liên hệ nếu chưa nhận được tiền");
      recommendations.push("✅ Quy trình hoàn tất");
    } else {
      recommendations.push("💰 BƯỚC 4: THỰC HIỆN THANH TOÁN");
      recommendations.push("🏦 Chuyển khoản đền bù cho shop");
      recommendations.push("📝 Cập nhật trạng thái hoàn tất");
      recommendations.push("📞 Xác nhận với shop đã nhận tiền");
      recommendations.push("📋 Lưu trữ hồ sơ xử lý");
      recommendations.push("✅ Đóng case");
    }

    return {
      ...state,
      currentStep: "complete",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        paymentStep: true,
        workflowComplete: true
      }
    };
  });

  // Define workflow edges
  workflow.addEdge(START, "verification");
  workflow.addEdge("verification", "documentation");
  workflow.addEdge("documentation", "approval");
  workflow.addEdge("approval", "payment");
  workflow.addEdge("payment", END);

  return workflow;
}

module.exports = {
  createLostPackageWorkflow
};
