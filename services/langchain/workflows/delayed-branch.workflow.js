"use strict";

const {StateGraph, END, START, Annotation} = require("@langchain/langgraph");

// Define the state schema
const WorkflowState = Annotation.Root({
  category: Annotation(),
  question: Annotation(),
  userType: Annotation(),
  businessContext: Annotation(),
  currentStep: Annotation(),
  recommendations: Annotation(),
  workflowData: Annotation(),
  orderCode: Annotation(),
  branchName: Annotation(),
  delayDuration: Annotation()
});

/**
 * Create Delayed at Branch Workflow
 * States: investigation → escalation → resolution
 * @returns {StateGraph} LangGraph workflow for delayed at branch handling
 */
function createDelayedAtBranchWorkflow() {
  const workflow = new StateGraph(WorkflowState);

  // Step 1: Investigation
  workflow.addNode("investigation", async (state) => {
    const recommendations = [];
    const orderCode = state.businessContext?.orderCode;
    
    if (state.userType === "shop") {
      recommendations.push("🔍 BƯỚC 1: ĐIỀU TRA NGUYÊN NHÂN");
      if (orderCode) {
        recommendations.push(`📦 Đơn hàng: ${orderCode}`);
      }
      recommendations.push("📅 Xác định thời gian hàng tồn tại bưu cục");
      recommendations.push("🏢 Xác định bưu cục đang giữ hàng");
      recommendations.push("❓ Tìm hiểu lý do chậm trễ:");
      recommendations.push("   • Quá tải hàng hóa");
      recommendations.push("   • Thiếu shipper");
      recommendations.push("   • Vấn đề kỹ thuật");
      recommendations.push("📞 CSKH sẽ liên hệ bưu cục ngay");
    } else {
      recommendations.push("🔍 BƯỚC 1: ĐIỀU TRA CHI TIẾT");
      if (orderCode) {
        recommendations.push(`📊 Tra cứu lịch sử đơn ${orderCode}`);
      }
      recommendations.push("📅 Kiểm tra thời gian tồn tại bưu cục");
      recommendations.push("🏢 Xác định bưu cục và lý do chậm trễ");
      recommendations.push("📞 Liên hệ trực tiếp bưu cục");
      recommendations.push("📋 Ghi nhận nguyên nhân cụ thể");
      recommendations.push("⚡ Yêu cầu giải pháp khắc phục");
    }

    return {
      ...state,
      currentStep: "escalation",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        investigationComplete: true,
        orderCode: orderCode
      }
    };
  });

  // Step 2: Escalation
  workflow.addNode("escalation", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("⚡ BƯỚC 2: LEO THANG XỬ LÝ");
      recommendations.push("🚨 CSKH đang leo thang vấn đề");
      recommendations.push("🏢 Liên hệ trực tiếp quản lý bưu cục");
      recommendations.push("⏰ Yêu cầu ưu tiên giao ngay trong ngày");
      recommendations.push("📝 Tạo ticket ưu tiên cao");
      recommendations.push("📞 Theo dõi sát sao tiến độ xử lý");
    } else {
      recommendations.push("⚡ BƯỚC 2: LEO THANG NGAY LẬP TỨC");
      recommendations.push("🚨 Liên hệ quản lý bưu cục");
      recommendations.push("📋 Báo cáo tình trạng chậm trễ");
      recommendations.push("⏰ Yêu cầu cam kết thời gian giao cụ thể");
      recommendations.push("📝 Tạo ghi chú ưu tiên trên đơn hàng");
      recommendations.push("📞 Thông báo shop về tiến độ xử lý");
    }

    return {
      ...state,
      currentStep: "resolution",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        escalationComplete: true
      }
    };
  });

  // Step 3: Resolution
  workflow.addNode("resolution", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("✅ BƯỚC 3: GIẢI QUYẾT");
      recommendations.push("🎯 Bưu cục đã cam kết giao trong ngày");
      recommendations.push("📞 Shipper sẽ liên hệ trước khi giao");
      recommendations.push("⏰ Theo dõi thời gian cam kết");
      recommendations.push("📱 Cập nhật trạng thái real-time");
      recommendations.push("📞 Liên hệ CSKH nếu vẫn chậm trễ");
      recommendations.push("✅ Quy trình hoàn tất");
    } else {
      recommendations.push("✅ BƯỚC 3: HOÀN TẤT GIẢI QUYẾT");
      recommendations.push("🎯 Đã có cam kết giao hàng từ bưu cục");
      recommendations.push("📞 Theo dõi thực hiện cam kết");
      recommendations.push("✅ Xác nhận giao hàng thành công");
      recommendations.push("📞 Liên hệ shop xác nhận kết quả");
      recommendations.push("📝 Cập nhật trạng thái hoàn tất");
      recommendations.push("✅ Đóng case chậm trễ");
    }

    return {
      ...state,
      currentStep: "complete",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        resolutionComplete: true,
        workflowComplete: true
      }
    };
  });

  // Define workflow edges
  workflow.addEdge(START, "investigation");
  workflow.addEdge("investigation", "escalation");
  workflow.addEdge("escalation", "resolution");
  workflow.addEdge("resolution", END);

  return workflow;
}

module.exports = {
  createDelayedAtBranchWorkflow
};
