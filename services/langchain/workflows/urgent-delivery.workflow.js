"use strict";

const {StateGraph, END, START, Annotation} = require("@langchain/langgraph");

// Define the state schema
const WorkflowState = Annotation.Root({
  category: Annotation(),
  question: Annotation(),
  userType: Annotation(),
  businessContext: Annotation(),
  currentStep: Annotation(),
  recommendations: Annotation(),
  workflowData: Annotation(),
  orderCode: Annotation(),
  urgencyLevel: Annotation(),
  deliveryTime: Annotation(),
  branchContacted: Annotation()
});

/**
 * Create Urgent Delivery Escalation Workflow
 * States: priority_assessment → branch_contact → tracking → updates
 * @returns {StateGraph} LangGraph workflow for urgent delivery escalation
 */
function createUrgentDeliveryWorkflow() {
  const workflow = new StateGraph(WorkflowState);

  // Step 1: Priority Assessment
  workflow.addNode("priority_assessment", async (state) => {
    const recommendations = [];
    const orderCode = state.businessContext?.orderCode;
    const isUrgent = state.businessContext?.urgentKeywords?.length > 0;
    
    if (state.userType === "shop") {
      recommendations.push("🚨 BƯỚC 1: ĐÁNH GIÁ ĐỘ ƯU TIÊN");
      if (orderCode) {
        recommendations.push(`📦 Đơn hàng: ${orderCode}`);
      }
      if (isUrgent) {
        recommendations.push("⚡ YÊU CẦU GIAO GẤP được xác nhận");
      }
      recommendations.push("⏰ Cung cấp thông tin chi tiết:");
      recommendations.push("   • Thời gian cụ thể khách cần nhận");
      recommendations.push("   • Lý do cần giao gấp");
      recommendations.push("   • Địa chỉ giao hàng chính xác");
      recommendations.push("📞 CSKH sẽ liên hệ bưu cục ngay");
    } else {
      recommendations.push("🚨 BƯỚC 1: XỬ LÝ YÊU CẦU GIAO GẤP");
      if (orderCode) {
        recommendations.push(`🔍 Tra cứu đơn ${orderCode}`);
      }
      recommendations.push("📊 Kiểm tra trạng thái và vị trí đơn hàng");
      recommendations.push("🏢 Xác định bưu cục đang xử lý");
      recommendations.push("⏰ Ghi nhận thời gian yêu cầu của khách");
      recommendations.push("📝 Tạo ghi chú ưu tiên trên đơn hàng");
    }

    return {
      ...state,
      currentStep: "branch_contact",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        priorityAssessed: true,
        urgencyLevel: isUrgent ? "high" : "medium"
      }
    };
  });

  // Step 2: Branch Contact
  workflow.addNode("branch_contact", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("📞 BƯỚC 2: LIÊN HỆ BƯU CỤC");
      recommendations.push("🏢 CSKH đang liên hệ bưu cục phụ trách");
      recommendations.push("⚡ Yêu cầu ưu tiên giao ngay");
      recommendations.push("📋 Thông báo thời gian cụ thể cho bưu cục");
      recommendations.push("🔔 Yêu cầu shipper gọi trước khi giao");
      recommendations.push("⏳ Chờ xác nhận từ bưu cục");
    } else {
      recommendations.push("📞 BƯỚC 2: LIÊN HỆ BƯU CỤC NGAY");
      recommendations.push("🏢 Gọi trực tiếp bưu cục đang xử lý");
      recommendations.push("⚡ Thông báo yêu cầu ưu tiên giao");
      recommendations.push("⏰ Cam kết thời gian giao cụ thể");
      recommendations.push("📝 Ghi nhận phản hồi từ bưu cục");
      recommendations.push("📞 Cập nhật cho shop về tiến độ");
    }

    return {
      ...state,
      currentStep: "tracking",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        branchContacted: true
      }
    };
  });

  // Step 3: Tracking
  workflow.addNode("tracking", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("📱 BƯỚC 3: THEO DÕI TIẾN ĐỘ");
      recommendations.push("✅ Bưu cục đã nhận yêu cầu ưu tiên");
      recommendations.push("🚚 Đơn hàng được ưu tiên trong lộ trình giao");
      recommendations.push("📞 Shipper sẽ gọi trước khi giao");
      recommendations.push("⏰ Theo dõi thời gian cam kết");
      recommendations.push("📱 CSKH sẽ cập nhật liên tục");
    } else {
      recommendations.push("📱 BƯỚC 3: THEO DÕI SÁT SAO");
      recommendations.push("🔍 Giám sát tiến độ xử lý tại bưu cục");
      recommendations.push("📞 Liên hệ định kỳ với shipper");
      recommendations.push("⏰ Đảm bảo đúng thời gian cam kết");
      recommendations.push("📱 Cập nhật real-time cho shop");
      recommendations.push("🚨 Báo cáo ngay nếu có vấn đề");
    }

    return {
      ...state,
      currentStep: "updates",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        trackingActive: true
      }
    };
  });

  // Step 4: Updates
  workflow.addNode("updates", async (state) => {
    const recommendations = [];
    
    if (state.userType === "shop") {
      recommendations.push("🔄 BƯỚC 4: CẬP NHẬT KẾT QUẢ");
      recommendations.push("✅ Đơn hàng đang được giao ưu tiên");
      recommendations.push("📞 Shipper sẽ liên hệ trước khi đến");
      recommendations.push("⏰ Dự kiến giao đúng thời gian yêu cầu");
      recommendations.push("📱 Theo dõi trạng thái trên app/website");
      recommendations.push("📞 Liên hệ CSKH nếu có vấn đề");
      recommendations.push("✅ Quy trình hoàn tất");
    } else {
      recommendations.push("🔄 BƯỚC 4: CẬP NHẬT CUỐI CÙNG");
      recommendations.push("✅ Xác nhận đơn hàng đã giao thành công");
      recommendations.push("📞 Liên hệ shop xác nhận kết quả");
      recommendations.push("📝 Cập nhật trạng thái hoàn tất");
      recommendations.push("📋 Lưu trữ thông tin xử lý");
      recommendations.push("✅ Đóng case giao gấp");
    }

    return {
      ...state,
      currentStep: "complete",
      recommendations: recommendations,
      workflowData: {
        ...state.workflowData,
        updatesComplete: true,
        workflowComplete: true
      }
    };
  });

  // Define workflow edges
  workflow.addEdge(START, "priority_assessment");
  workflow.addEdge("priority_assessment", "branch_contact");
  workflow.addEdge("branch_contact", "tracking");
  workflow.addEdge("tracking", "updates");
  workflow.addEdge("updates", END);

  return workflow;
}

module.exports = {
  createUrgentDeliveryWorkflow
};
