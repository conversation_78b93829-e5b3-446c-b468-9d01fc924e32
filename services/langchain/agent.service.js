"use strict";

const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const {MemorySaver} = require("@langchain/langgraph");

// Import workflow modules
const {createLostPackageWorkflow} = require("./workflows/lost-package.workflow");
const {createGH1PWorkflow} = require("./workflows/gh1p-decision.workflow");
const {createUrgentDeliveryWorkflow} = require("./workflows/urgent-delivery.workflow");
const {createCODModificationWorkflow} = require("./workflows/cod-modification.workflow");
const {createDelayedAtBranchWorkflow} = require("./workflows/delayed-branch.workflow");

// Import business logic handlers
const {
  handleLostPackageLogic,
  handleCODModificationLogic,
  handleDeliveryReminderLogic,
  handleDelayedAtBranchLogic,
  handleGH1PModificationLogic
} = require("./handlers/business-logic.handler");

// Import utility functions
const {extractBusinessContext} = require("./utils/context-extractor");
const {formatWorkflowResponse} = require("./utils/workflow-formatter");

/**
 * Agent Service
 * Handles AI-powered customer support with LangGraph workflows
 */
module.exports = {
  name: "agent",

  /**
   * Settings
   */
  settings: {
    /** Default confidence threshold for business rule matching */
    defaultConfidenceThreshold: 0.7,

    /** Default limit for business rule search */
    defaultSearchLimit: 5,

    /** Default GPT model */
    defaultModel: "gpt-4o-mini",

    /** Default temperature for GPT */
    defaultTemperature: 0.3,

    /** LangGraph workflow settings */
    workflows: {
      /** Enable LangGraph workflows */
      enabled: true,

      /** Workflow timeout in milliseconds */
      timeout: 5 * 60 * 1000, // 5 minutes

      /** Maximum workflow steps */
      maxSteps: 10,

      /** Workflow memory settings */
      memory: {
        /** Memory TTL in seconds */
        ttl: 30 * 60, // 30 minutes

        /** Maximum conversations in memory */
        maxConversations: 1000
      }
    }
  },

  /**
   * Dependencies
   */
  dependencies: ["langchain", "business", "conversations", "messages"],

  /**
   * Actions
   */
  actions: {
    /**
     * Ask AI agent with business logic integration
     */
    ask: {
      rest: "POST /ask",
      auth: "required",
      timeout: 2 * 60 * 1000, // 2 minutes timeout
      params: {
        question: {type: "string", min: 1, max: 1000},
        conversationId: {type: "string", optional: true},
        userId: {type: "string"},
        userType: {type: "string", enum: ["shop", "cskh"]},
        includeBusinessLogic: {type: "boolean", default: true},
        confidenceThreshold: {type: "number", optional: true, min: 0, max: 1},
        searchLimit: {type: "number", optional: true, min: 1, max: 20}
      },
      async handler(ctx) {
        const {
          question,
          conversationId,
          userId,
          userType,
          includeBusinessLogic,
          confidenceThreshold = this.settings.defaultConfidenceThreshold,
          searchLimit = this.settings.defaultSearchLimit
        } = ctx.params;

        try {
          this.logger.info(`Processing question from ${userType}: ${question.substring(0, 100)}...`);

          // Step 1: Find related business rules if needed
          let relatedBusinessRules = [];
          if (includeBusinessLogic) {
            relatedBusinessRules = await ctx.call("business.search", {
              query: question,
              threshold: confidenceThreshold,
              limit: searchLimit
            });
          }

          // Step 2: Apply business logic if needed and rules found
          let businessLogicResult = null;
          if (includeBusinessLogic && relatedBusinessRules.length > 0) {
            businessLogicResult = await this.applyBusinessLogic(ctx, {
              question,
              relatedRules: relatedBusinessRules,
              userType,
              conversationId
            });
          }

          // Step 3: Generate AI response
          const aiResponse = await this.generateLLMResponse(ctx, {
            question,
            userType,
            businessLogicResult,
            relatedBusinessRules
          });

          // Step 4: Save conversation if conversationId provided
          if (conversationId) {
            // Save user message
            await ctx.call("messages.create", {
              conversationId: conversationId,
              senderId: userId,
              senderRole: userType,
              text: question
            });

            // Save AI response
            await ctx.call("messages.create", {
              conversationId: conversationId,
              senderId: ctx.meta.user._id,
              senderRole: "support",
              text: aiResponse
            });
          }

          return {
            answer: aiResponse,
            businessLogic: businessLogicResult,
            relatedRules: relatedBusinessRules,
            conversationId: conversationId
          };

        } catch (error) {
          this.logger.error("Error processing question:", error);
          throw new MoleculerClientError(
            i18next.t("Failed to process question: ") + error.message,
            500,
            "QUESTION_PROCESSING_ERROR",
            {originalError: error.message}
          );
        }
      }
    },

    /**
     * Continue workflow execution for multi-step conversations
     */
    continueWorkflow: {
      rest: "POST /workflow/continue",
      auth: "required",
      timeout: 2 * 60 * 1000, // 2 minutes timeout
      params: {
        conversationId: {type: "string"},
        userResponse: {type: "string", min: 1, max: 1000},
        userId: {type: "string"},
        userType: {type: "string", enum: ["shop", "cskh"]},
        workflowState: {type: "object", optional: true}
      },
      async handler(ctx) {
        const {conversationId, userResponse, userId, userType, workflowState} = ctx.params;

        try {
          this.logger.info(`Continuing workflow for conversation ${conversationId}`);

          if (!workflowState || !workflowState.category) {
            throw new MoleculerClientError("Invalid workflow state", 400);
          }

          // Update workflow state with user response
          const updatedState = {
            ...workflowState,
            userResponse: userResponse,
            lastUpdate: new Date().toISOString()
          };

          // Continue workflow execution
          const workflowResult = await this.executeWorkflow(ctx, updatedState);

          // Save user response as message
          await ctx.call("messages.create", {
            conversationId: conversationId,
            senderId: userId,
            senderRole: userType,
            text: userResponse
          });

          // Generate and save AI response
          const aiResponse = this.formatWorkflowResponse(workflowResult);
          await ctx.call("messages.create", {
            conversationId: conversationId,
            senderId: ctx.meta.user._id,
            senderRole: "support",
            text: aiResponse
          });

          return {
            answer: aiResponse,
            workflowState: workflowResult.state,
            workflowSteps: workflowResult.steps,
            isWorkflowComplete: workflowResult.isComplete,
            conversationId: conversationId,
            nextStep: workflowResult.state?.currentStep
          };

        } catch (error) {
          this.logger.error("Error continuing workflow:", error);
          throw new MoleculerClientError(
            i18next.t("Failed to continue workflow: ") + error.message,
            500,
            "WORKFLOW_CONTINUATION_ERROR",
            {originalError: error.message}
          );
        }
      }
    },

    /**
     * Get conversation history with AI responses
     */
    getConversationHistory: {
      rest: "GET /conversations/:conversationId/history",
      auth: "required",
      params: {
        conversationId: {type: "string"},
        limit: {type: "number", optional: true, default: 50, min: 1, max: 100},
        offset: {type: "number", optional: true, default: 0, min: 0}
      },
      async handler(ctx) {
        const {conversationId, limit, offset} = ctx.params;

        try {
          const messages = await ctx.call("messages.list", {
            conversationId,
            limit,
            offset,
            sort: "-createdAt"
          });

          return {
            conversationId,
            messages: messages.rows || [],
            total: messages.total || 0,
            limit,
            offset
          };

        } catch (error) {
          this.logger.error("Error getting conversation history:", error);
          throw new MoleculerClientError(
            i18next.t("Failed to get conversation history: ") + error.message,
            500,
            "CONVERSATION_HISTORY_ERROR",
            {originalError: error.message}
          );
        }
      }
    },

    /**
     * Get agent statistics
     */
    getStats: {
      rest: "GET /stats",
      auth: "required",
      async handler(ctx) {
        try {
          const stats = {
            workflowsEnabled: this.settings.workflows.enabled,
            memorySize: this.workflowMemory ? this.workflowMemory.size : 0,
            uptime: process.uptime(),
            timestamp: new Date().toISOString()
          };

          return stats;

        } catch (error) {
          this.logger.error("Error getting stats:", error);
          throw new MoleculerClientError(
            i18next.t("Failed to get stats: ") + error.message,
            500,
            "STATS_ERROR",
            {originalError: error.message}
          );
        }
      }
    }
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    /**
     * Apply business logic using LangGraph workflows
     */
    async applyBusinessLogic(ctx, {question, relatedRules, userType, conversationId}) {
      try {
        // Extract business context from question
        const businessContext = extractBusinessContext(question);

        const topRule = relatedRules[0];
        if (!topRule) return null;

        // Check if LangGraph workflows are enabled
        if (!this.settings.workflows.enabled) {
          return await this.applySimpleBusinessLogic(ctx, {question, relatedRules, userType, businessContext});
        }

        // Initialize workflow state
        const workflowState = {
          category: topRule.category,
          question: question,
          userType: userType,
          businessContext: businessContext,
          conversationId: conversationId,
          confidence: topRule.similarityScore,
          currentStep: "initial",
          recommendations: [],
          workflowData: {},
          timestamp: new Date().toISOString()
        };

        // Execute LangGraph workflow based on category
        const workflowResult = await this.executeWorkflow(ctx, workflowState);

        return {
          appliedRule: topRule.category,
          confidence: topRule.similarityScore,
          recommendations: workflowResult.recommendations,
          extractedContext: businessContext,
          workflowState: workflowResult.state,
          workflowSteps: workflowResult.steps,
          isWorkflowComplete: workflowResult.isComplete
        };

      } catch (error) {
        this.logger.error("Error applying LangGraph business logic:", error);

        // Fallback to simple business logic
        return await this.applySimpleBusinessLogic(ctx, {
          question,
          relatedRules,
          userType,
          businessContext: extractBusinessContext(question)
        });
      }
    },

    /**
     * Fallback simple business logic (original implementation)
     */
    async applySimpleBusinessLogic(ctx, {question, relatedRules, userType, businessContext}) {
      const topRule = relatedRules[0];
      if (!topRule) return null;

      const result = {
        appliedRule: topRule.category,
        confidence: topRule.similarityScore,
        recommendations: [],
        extractedContext: businessContext
      };

      // Apply category-specific logic using imported handlers
      switch (topRule.category) {
        case "lost_package":
        case "lost_package_compensation":
          result.recommendations = await handleLostPackageLogic(ctx, {question, userType});
          break;

        case "cod_modification":
        case "address_modification":
          result.recommendations = await handleCODModificationLogic(ctx, {question, userType});
          break;

        case "delivery_reminder":
        case "urgent_delivery":
          result.recommendations = await handleDeliveryReminderLogic(ctx, {question, userType});
          break;

        case "delayed_at_branch":
          result.recommendations = await handleDelayedAtBranchLogic(ctx, {question, userType});
          break;

        case "gh1p_modification":
          result.recommendations = await handleGH1PModificationLogic(ctx, {question, userType});
          break;

        default:
          result.recommendations = ["Chuyển tiếp cho nhân viên hỗ trợ chuyên biệt"];
      }

      return result;
    },

    /**
     * Execute LangGraph workflow based on category
     */
    async executeWorkflow(ctx, initialState) {
      try {
        const workflowGraph = this.createWorkflowGraph(initialState.category);

        if (!workflowGraph) {
          this.logger.warn(`No workflow graph found for category: ${initialState.category}`);
          return {
            recommendations: ["Chuyển tiếp cho nhân viên hỗ trợ chuyên biệt"],
            state: initialState,
            steps: [],
            isComplete: true
          };
        }

        // Create memory saver for conversation state
        const memory = new MemorySaver();
        const app = workflowGraph.compile({checkpointer: memory});

        // Execute workflow
        const config = {
          configurable: {
            thread_id: initialState.conversationId || `workflow_${Date.now()}`
          }
        };

        const steps = [];
        let finalState = initialState;

        // Run workflow with step tracking
        for await (const step of await app.stream(initialState, config)) {
          steps.push({
            step: Object.keys(step)[0],
            state: Object.values(step)[0],
            timestamp: new Date().toISOString()
          });
          finalState = Object.values(step)[0];
        }

        return {
          recommendations: finalState.recommendations || [],
          state: finalState,
          steps: steps,
          isComplete: finalState.currentStep === "complete"
        };

      } catch (error) {
        this.logger.error("Error executing workflow:", error);
        throw error;
      }
    },

    /**
     * Create workflow graph based on business category
     */
    createWorkflowGraph(category) {
      switch (category) {
        case "lost_package":
        case "lost_package_compensation":
          return createLostPackageWorkflow();

        case "gh1p_edit":
        case "gh1p_modification":
          return createGH1PWorkflow();

        case "rush_delivery":
        case "delivery_reminder":
        case "urgent_delivery":
          return createUrgentDeliveryWorkflow();

        case "cod_edit":
        case "cod_modification":
        case "address_modification":
          return createCODModificationWorkflow();

        case "delayed_at_branch":
          return createDelayedAtBranchWorkflow();

        case "return_request":
        case "complaint":
        case "inquiry":
        case "other":
          // For these categories, use simple business logic without complex workflows
          return null;

        default:
          return null;
      }
    },

    /**
     * Generate LLM response with context from business rules
     */
    async generateLLMResponse(ctx, {question, userType, businessLogicResult, relatedBusinessRules}) {
      try {
        // Build context from business logic
        let context = "";
        if (businessLogicResult && businessLogicResult.recommendations) {
          context += "Khuyến nghị từ hệ thống:\n";
          context += businessLogicResult.recommendations.join("\n");
          context += "\n\n";
        }

        if (relatedBusinessRules && relatedBusinessRules.length > 0) {
          context += "Quy tắc nghiệp vụ liên quan:\n";
          relatedBusinessRules.forEach((rule, index) => {
            context += `${index + 1}. ${rule.category}: ${rule.response || 'Không có mô tả'}\n`;
          });
          context += "\n";
        }

        // Build system prompt
        const systemPrompt = this.buildSystemPrompt(userType, context);

        // Build messages array for chatCompletion
        const messages = [];
        if (systemPrompt) {
          messages.push({role: "system", content: systemPrompt});
        }
        messages.push({role: "user", content: question});

        // Call LangChain service for LLM response
        const response = await ctx.call("langchain.chatCompletion", {
          messages: messages,
          model: this.settings.defaultModel,
          temperature: this.settings.defaultTemperature
        });

        return response;

      } catch (error) {
        this.logger.error("Error generating LLM response:", error);

        // Fallback response
        if (businessLogicResult && businessLogicResult.recommendations) {
          return businessLogicResult.recommendations.join("\n");
        }

        return "Xin lỗi, tôi không thể xử lý câu hỏi này lúc này. Vui lòng liên hệ nhân viên hỗ trợ.";
      }
    },

    /**
     * Build system prompt based on user type and context
     */
    buildSystemPrompt(userType, context) {
      const basePrompt = `Bạn là trợ lý AI chuyên nghiệp của hệ thống chăm sóc khách hàng GHVN.
Nhiệm vụ của bạn là hỗ trợ ${userType === "shop" ? "shop/đối tác" : "nhân viên CSKH"} giải quyết các vấn đề liên quan đến đơn hàng và dịch vụ.

CHUYÊN MÔN NGHIỆP VỤ:
• Xử lý đơn hàng thất lạc/mất và quy trình đền bù
• Hỗ trợ yêu cầu giao hàng gấp và ưu tiên
• Xử lý đơn hàng tồn tại bưu cục quá lâu
• Sửa đổi COD, địa chỉ giao hàng
• Xử lý GH1P (Giao 1 Phần) và GTB TT (Giao Thành Bộ Thanh Toán)

NGUYÊN TẮC XỬ LÝ:
1. Luôn lịch sự, chuyên nghiệp và thấu hiểu
2. Ưu tiên giải pháp nhanh chóng, hiệu quả
3. Cung cấp thông tin chính xác dựa trên quy trình chuẩn
4. Hướng dẫn từng bước cụ thể, rõ ràng
5. Sử dụng emoji để làm nổi bật thông tin quan trọng
6. ${userType === "shop" ? "Hỗ trợ shop giải quyết vấn đề với khách hàng" : "Hướng dẫn CSKH xử lý chuyên nghiệp"}

QUY TRÌNH ĐỀN BÙ HÀNG MẤT:
• Có bảo hiểm: Đền theo giá trị khai báo
• Không bảo hiểm: Tối đa 4 lần cước phí vận chuyển
• Yêu cầu chứng từ: ảnh chuyển khoản, tin nhắn đặt hàng
• Thời gian xử lý: 3-5 ngày làm việc

KHUYẾN NGHỊ GH1P:
• Ưu tiên sử dụng GTB TT thay vì GH1P để tránh rủi ro
• GTB TT: Hạ COD GTB TT, giữ nguyên COD gốc, ghi chú chi tiết

${context}

Hãy trả lời một cách ngắn gọn, rõ ràng và hữu ích. Sử dụng emoji để làm nổi bật thông tin quan trọng.`;

      return basePrompt;
    },

    /**
     * Format workflow response for user display
     */
    formatWorkflowResponse(workflowResult) {
      return formatWorkflowResponse(workflowResult);
    },

    /**
     * Start workflow memory cleanup interval
     */
    startWorkflowMemoryCleanup() {
      const cleanupInterval = 5 * 60 * 1000; // 5 minutes
      const memoryTTL = this.settings.workflows.memory.ttl * 1000; // Convert to milliseconds

      this.workflowMemoryCleanupInterval = setInterval(() => {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, value] of this.workflowMemory.entries()) {
          if (now - value.timestamp > memoryTTL) {
            this.workflowMemory.delete(key);
            cleanedCount++;
          }
        }

        if (cleanedCount > 0) {
          this.logger.info(`Cleaned up ${cleanedCount} expired workflow memories`);
        }

        // Enforce max conversations limit
        if (this.workflowMemory.size > this.settings.workflows.memory.maxConversations) {
          const entries = Array.from(this.workflowMemory.entries());
          entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

          const toRemove = entries.slice(0, this.workflowMemory.size - this.settings.workflows.memory.maxConversations);
          toRemove.forEach(([key]) => this.workflowMemory.delete(key));

          this.logger.info(`Removed ${toRemove.length} oldest workflow memories to enforce limit`);
        }
      }, cleanupInterval);
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    this.logger.info("Agent service created");

    // Initialize workflow memory
    this.workflowMemory = new Map();
    this.workflowMemoryCleanupInterval = null;
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    this.logger.info("Agent service started");

    // Verify dependencies
    try {
      await this.broker.waitForServices(["langchain", "business", "conversations", "messages"]);
      this.logger.info("All required services are available");
    } catch (error) {
      this.logger.error("Some required services are not available:", error);
    }

    // Start workflow memory cleanup
    if (this.settings.workflows.enabled) {
      this.startWorkflowMemoryCleanup();
      this.logger.info("LangGraph workflows enabled and memory cleanup started");
    }
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
    this.logger.info("Agent service stopped");

    // Stop workflow memory cleanup
    if (this.workflowMemoryCleanupInterval) {
      clearInterval(this.workflowMemoryCleanupInterval);
    }
  }
};
