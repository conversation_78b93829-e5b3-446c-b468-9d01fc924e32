"use strict";

const DbMongoose = require("../../mixins/dbMongo.mixin");
const BUSINESS = require("./business.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const fs = require("fs");

module.exports = {
  name: "business",
  mixins: [DbMongoose(BUSINESS), BaseService, FunctionsCommon],

  /**
   * Default settings
   */
  settings: {

    /** Public fields */
    fields: [
      "_id", "category", "keywords", "examples", "response", "embedding",
      "status", "priority", "tags", "createdBy", "updatedBy", "createdAt", "updatedAt"
    ],

    /** Validator schema for entity */
    entityValidator: {
      category: {
        type: "string",
        enum: ["lost_package", "rush_delivery", "cod_edit", "delayed_at_branch", "gh1p_edit", "return_request", "complaint", "inquiry", "other"]
      },
      keywords: {type: "array", items: "string", min: 1},
      examples: {type: "array", items: "string", optional: true},
      response: {type: "string", min: 1},
      status: {type: "string", enum: ["active", "inactive", "draft"], optional: true},
      priority: {type: "number", optional: true},
      tags: {type: "array", items: "string", optional: true}
    },

    /** Populate options */
    populates: {
      createdBy: {
        action: "users.get"
      },
      updatedBy: {
        action: "users.get"
      }
    },
    populateOptions: ["createdBy", "updatedBy"],
  },

  /**
   * Actions
   */
  actions: {
    /**
     * Create business rule with auto-generated embedding
     */
    create: {
      rest: "POST /",
      auth: "required",
      params: {
        category: {type: "string"},
        keywords: {type: "array", items: "string"},
        examples: {type: "array", items: "string", optional: true},
        response: {type: "string"},
        status: {type: "string", optional: true},
        priority: {type: "number", optional: true},
        tags: {type: "array", items: "string", optional: true}
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const entity = ctx.params;

        // Create text content for embedding
        const textContent = this.createTextForEmbedding(entity);

        // Generate embedding
        const embedding = await ctx.call("langchain.getEmbedding", {textContent});

        // Create business rule
        const businessRule = {
          category: entity.category,
          keywords: entity.keywords,
          examples: entity.examples || [],
          response: entity.response,
          embedding: embedding,
          status: entity.status || "active",
          priority: entity.priority || 0,
          tags: entity.tags || [],
          createdBy: user._id,
          updatedBy: user._id
        };

        const result = await this.adapter.insert(businessRule);
        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, result);
      }
    },

    /**
     * Update business rule with embedding regeneration
     */
    update: {
      rest: "PUT /:id",
      auth: "required",
      params: {
        id: "string",
        category: {type: "string", optional: true},
        keywords: {type: "array", items: "string", optional: true},
        examples: {type: "array", items: "string", optional: true},
        response: {type: "string", optional: true},
        status: {type: "string", optional: true},
        priority: {type: "number", optional: true},
        tags: {type: "array", items: "string", optional: true}
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const {id, ...updateData} = ctx.params;

        // Check if business rule exists
        const existing = await this.adapter.findById(id);
        if (!existing) {
          throw new MoleculerClientError(i18next.t("Business rule not found"), 404);
        }

        // If content fields changed, regenerate embedding
        const contentFields = ['category', 'keywords', 'examples', 'response'];
        const shouldUpdateEmbedding = contentFields.some(field => updateData[field] !== undefined);

        if (shouldUpdateEmbedding) {
          const mergedData = {...existing, ...updateData};
          const textContent = this.createTextForEmbedding(mergedData);
          updateData.embedding = await ctx.call("langchain.getEmbedding", {textContent});
        }

        updateData.updatedBy = user._id;

        const result = await this.adapter.updateById(id, updateData);
        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, result);
      }
    },

    /**
     * Search business rules by similarity
     */
    search: {
      rest: "POST /search",
      auth: "required",
      params: {
        query: "string",
        limit: {type: "number", optional: true, default: 10},
        threshold: {type: "number", optional: true, default: 0.7},
        category: {type: "string", optional: true}
      },
      async handler(ctx) {
        const {query, limit, threshold, category} = ctx.params;

        // Generate embedding for search query
        const queryEmbedding = await ctx.call("langchain.getEmbedding", {textContent: query});

        // Get all active business rules
        const filter = {
          status: "active",
          isDeleted: false,
          embedding: {$exists: true, $ne: []}
        };

        if (category) {
          filter.category = category;
        }

        const businessRules = await this.adapter.find({query: filter});

        // Calculate similarity scores
        const results = [];
        for (const rule of businessRules) {
          if (rule.embedding && rule.embedding.length > 0) {
            const score = await ctx.call("langchain.getRelatedScore", {
              vectors: [queryEmbedding, rule.embedding]
            });

            if (score >= threshold) {
              results.push({
                rule: rule,
                similarityScore: score
              });
            }
          }
        }

        // Sort by similarity score and limit results
        results.sort((a, b) => b.similarityScore - a.similarityScore);
        const limitedResults = results.slice(0, limit);

        // Transform the actual Mongoose documents and add similarity scores
        const transformedResults = [];
        for (const item of limitedResults) {
          const transformedRule = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, item.rule);
          transformedResults.push({
            ...transformedRule,
            similarityScore: item.similarityScore
          });
        }

        return transformedResults;
      }
    },

    /**
     * Find similar business rules
     */
    findSimilar: {
      rest: "GET /:id/similar",
      auth: "required",
      params: {
        id: "string",
        limit: {type: "number", optional: true, default: 5},
        threshold: {type: "number", optional: true, default: 0.6}
      },
      async handler(ctx) {
        const {id, limit, threshold} = ctx.params;

        // Get the business rule
        const businessRule = await this.adapter.findById(id);
        if (!businessRule || !businessRule.embedding) {
          throw new MoleculerClientError(i18next.t("Business rule not found or no embedding"), 404);
        }

        // Get all other active business rules
        const otherRules = await this.adapter.find({
          query: {
            _id: {$ne: id},
            status: "active",
            isDeleted: false,
            embedding: {$exists: true, $ne: []}
          }
        });

        // Calculate similarity scores
        const results = [];
        for (const rule of otherRules) {
          if (rule.embedding && rule.embedding.length > 0) {
            const score = await ctx.call("langchain.getRelatedScore", {
              vectors: [businessRule.embedding, rule.embedding]
            });

            if (score >= threshold) {
              results.push({
                ...rule,
                similarityScore: score
              });
            }
          }
        }

        // Sort by similarity score and limit results
        results.sort((a, b) => b.similarityScore - a.similarityScore);
        return results.slice(0, limit);
      }
    },

    /**
     * Get business rules by category
     */
    getByCategory: {
      rest: "GET /category/:category",
      auth: "required",
      params: {
        category: "string",
        status: {type: "string", optional: true}
      },
      async handler(ctx) {
        const {category, status} = ctx.params;

        const query = {
          category,
          isDeleted: false
        };

        if (status) {
          query.status = status;
        }

        const result = await this.adapter.find({
          query,
          sort: ["-priority", "-createdAt"]
        });

        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, result);
      }
    },

    /**
     * Import business rules from JSON
     */
    importData: {
      rest: "POST /import",
      auth: "required",
      params: {
        data: {type: "array", items: "object"},
        overwrite: {type: "boolean", optional: true, default: false}
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const {data, overwrite} = ctx.params;

        const results = {
          imported: 0,
          updated: 0,
          errors: []
        };

        for (const item of data) {
          try {
            // Validate required fields
            if (!item.category || !item.keywords || !item.response) {
              results.errors.push({
                item,
                error: "Missing required fields: category, keywords, or response"
              });
              continue;
            }

            // Create text content for embedding
            const textContent = this.createTextForEmbedding(item);
            const embedding = await ctx.call("langchain.getEmbedding", {textContent});

            // Check if similar rule exists
            const existing = await this.adapter.findOne({
              category: item.category,
              keywords: {$in: item.keywords}
            });

            if (existing && !overwrite) {
              results.errors.push({
                item,
                error: "Similar rule already exists"
              });
              continue;
            }

            const businessRule = {
              category: item.category,
              keywords: item.keywords,
              examples: item.examples || [],
              response: item.response,
              embedding: embedding,
              status: item.status || "active",
              priority: item.priority || 0,
              tags: item.tags || [],
              createdBy: user._id,
              updatedBy: user._id
            };

            if (existing && overwrite) {
              await this.adapter.updateById(existing._id, businessRule);
              results.updated++;
            } else {
              await this.adapter.insert(businessRule);
              results.imported++;
            }

          } catch (error) {
            results.errors.push({
              item,
              error: error.message
            });
          }
        }

        return results;
      }
    },

    /**
     * Export business rules to JSON
     */
    exportData: {
      rest: "GET /export",
      auth: "required",
      params: {
        category: {type: "string", optional: true},
        status: {type: "string", optional: true},
        includeEmbedding: {type: "boolean", optional: true, default: false}
      },
      async handler(ctx) {
        const {category, status, includeEmbedding} = ctx.params;

        const query = {isDeleted: false};

        if (category) query.category = category;
        if (status) query.status = status;

        const businessRules = await this.adapter.find({query});

        // Format data for export
        const exportData = businessRules.map(rule => {
          const exported = {
            category: rule.category,
            keywords: rule.keywords,
            examples: rule.examples,
            response: rule.response,
            status: rule.status,
            priority: rule.priority,
            tags: rule.tags
          };

          if (includeEmbedding) {
            exported.embedding = rule.embedding;
          }

          return exported;
        });

        return {
          data: exportData,
          count: exportData.length,
          exportedAt: new Date()
        };
      }
    },

    /**
     * Update embedding for a specific business rule
     */
    updateEmbedding: {
      rest: "PUT /:id/embedding",
      auth: "required",
      params: {
        id: "string"
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const {id} = ctx.params;

        const businessRule = await this.adapter.findById(id);
        if (!businessRule) {
          throw new MoleculerClientError(i18next.t("Business rule not found"), 404);
        }

        // Generate new embedding
        const textContent = this.createTextForEmbedding(businessRule);
        const embedding = await ctx.call("langchain.getEmbedding", {textContent});

        // Update the business rule
        const result = await this.adapter.updateById(id, {
          embedding: embedding,
          updatedBy: user._id
        });

        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, result);
      }
    },

    /**
     * Bulk update embeddings for all business rules
     */
    bulkUpdateEmbeddings: {
      rest: "PUT /embeddings/bulk",
      auth: "required",
      params: {
        category: {type: "string", optional: true},
        status: {type: "string", optional: true}
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const {category, status} = ctx.params;

        const query = {isDeleted: false};
        if (category) query.category = category;
        if (status) query.status = status;

        const businessRules = await this.adapter.find({query});

        const results = {
          updated: 0,
          errors: []
        };

        for (const rule of businessRules) {
          try {
            const textContent = this.createTextForEmbedding(rule);
            const embedding = await ctx.call("langchain.getEmbedding", {textContent});

            await this.adapter.updateById(rule._id, {
              embedding: embedding,
              updatedBy: user._id
            });

            results.updated++;
          } catch (error) {
            results.errors.push({
              id: rule._id,
              error: error.message
            });
          }
        }

        return results;
      }
    },

    /**
     * Get business statistics
     */
    getStats: {
      rest: "GET /stats",
      auth: "required",
      cache: {
        keys: [],
        ttl: 60 // 1 minute cache
      },
      async handler(ctx) {
        const stats = await Promise.all([
          // Total count
          this.adapter.count({isDeleted: false}),
          // Active count
          this.adapter.count({status: "active", isDeleted: false}),
          // Inactive count
          this.adapter.count({status: "inactive", isDeleted: false}),
          // Draft count
          this.adapter.count({status: "draft", isDeleted: false}),
          // By category
          this.adapter.collection.aggregate([
            {$match: {isDeleted: false}},
            {$group: {_id: "$category", count: {$sum: 1}}},
            {$sort: {count: -1}}
          ]).toArray(),
          // With embeddings
          this.adapter.count({
            embedding: {$exists: true, $ne: []},
            isDeleted: false
          })
        ]);

        return {
          total: stats[0],
          active: stats[1],
          inactive: stats[2],
          draft: stats[3],
          byCategory: stats[4],
          withEmbeddings: stats[5]
        };
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    /**
     * Create text content for embedding generation
     */
    createTextForEmbedding(businessRule) {
      const parts = [];

      // Add category
      parts.push(`Category: ${businessRule.category}`);

      // Add keywords
      if (businessRule.keywords && businessRule.keywords.length > 0) {
        parts.push(`Keywords: ${businessRule.keywords.join(', ')}`);
      }

      // Add examples
      if (businessRule.examples && businessRule.examples.length > 0) {
        parts.push(`Examples: ${businessRule.examples.join('. ')}`);
      }

      // Add response
      parts.push(`Response: ${businessRule.response}`);

      // Add tags
      if (businessRule.tags && businessRule.tags.length > 0) {
        parts.push(`Tags: ${businessRule.tags.join(', ')}`);
      }

      return parts.join('\n');
    },

    /**
     * Find business rules by keywords
     */
    async findByKeywords(keywords, options = {}) {
      const query = {
        keywords: {$in: keywords},
        status: "active",
        isDeleted: false,
        ...options.query
      };

      return this.adapter.find({
        query,
        sort: options.sort || ["-priority", "-createdAt"],
        limit: options.limit,
        offset: options.offset
      });
    }
  },

  /**
   * Events
   */
  events: {
    /**
     * Handle business rule created event
     */
    "business.created"(payload) {
      this.logger.info("Business rule created:", payload._id);
    },

    /**
     * Handle business rule updated event
     */
    "business.updated"(payload) {
      this.logger.info("Business rule updated:", payload._id);
    }
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    // Create indexes if needed
    if (this.adapter.collection) {
      this.logger.info("Creating indexes for business collection...");
      await this.adapter.collection.createIndex({category: 1, status: 1});
      await this.adapter.collection.createIndex({keywords: 1});
      await this.adapter.collection.createIndex({tags: 1});
      await this.adapter.collection.createIndex({priority: -1, createdAt: -1});
      await this.adapter.collection.createIndex({createdBy: 1});
      await this.adapter.collection.createIndex({isDeleted: 1});
    }
  }
};
