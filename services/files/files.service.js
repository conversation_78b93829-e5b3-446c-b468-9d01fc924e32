"use strict";

const DbMongoose = require("../../mixins/dbMongo.mixin");
const { MoleculerClientError } = require("moleculer").Errors;
const FILES = require("./files.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const FileMixin = require("../../mixins/file.mixin");
const path = require("path");
const fs = require("fs");
const i18next = require("i18next");

// Define storage directory
const uploadDir = path.join(__dirname, "./storage");

module.exports = {
  name: "files",
  mixins: [DbMongoose(FILES), BaseService, FunctionsCommon, FileMixin],

  /**
   * Default settings
   */
  settings: {
    /** REST Basepath */
    rest: "/files",

    /** Public fields */
    fields: [
      "_id", "ownerId", "name", "displayName", "fileType", "mimetype",
      "size", "storageType", "storageLocation", "used", "createdAt", "updatedAt"
    ],

    /** Validator schema for entity */
    entityValidator: {
      ownerId: { type: "string" },
      name: { type: "string" },
      displayName: { type: "string" },
      fileType: { type: "string" },
      mimetype: { type: "string" },
      size: { type: "string" },
      storageType: { type: "string", optional: true },
      storageLocation: { type: "string" },
      used: { type: "boolean", optional: true },
    },
  },

  /**
   * Actions
   */
  actions: {
    /**
     * Upload a file
     *
     * @actions
     * @param {Object} meta - Contains user information
     * @returns {Object} Created file entity
     */
    upload: {
      auth: "required",
      params: {},
      async handler(ctx) {
        // Check if user is authenticated
        const {filename, mimetype} = ctx.meta;
        const uniqueFileName = this.createUniqueFileName(filename);

        if (!ctx.meta.user) {
          throw new MoleculerClientError(i18next.t("no_permission"), 403);
        }

        // Create storage directory if it doesn't exist
        this.createFolderIfNotExist(uploadDir);

        // Generate unique filename to prevent collisions
        const filePath = path.join(uploadDir, uniqueFileName);

        try {
          // Save file to storage
          await this.saveToLocalStorage(ctx.params, filePath);
          let stat = fs.statSync(filePath);
          // Determine file type based on mimetype
          let fileType = "file";
          if (mimetype.startsWith("image/")) {
            fileType = "image";
          } else if (mimetype.startsWith("audio/")) {
            fileType = "audio";
          } else if (mimetype.startsWith("video/")) {
            fileType = "video";
          }

          // Create file record in database
          const fileData = {
            ownerId: ctx.meta.user._id,
            name: uniqueFileName,
            displayName: filename,
            fileType: fileType,
            mimetype: mimetype,
            size: stat.size,
            storageType: "local_storage",
            storageLocation: "storage"
          };

          // Insert file record
          const doc = await this.adapter.insert(fileData);

          // Return file entity
          return await this.transformDocuments(ctx, {}, doc);
        } catch (error) {
          // Clean up file if it was created
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }

          throw new MoleculerClientError(
            i18next.t("file_upload_failed") || "File upload failed",
            500,
            "FILE_UPLOAD_ERROR"
          );
        }
      }
    },

    stream: {
      rest: 'GET /content/:id',
      // auth: "required",
      async handler(ctx) {
        try {
          const {id} = ctx.params;
          const file = await this.adapter.findById(id);
          console.log("file", file);
          if (!file) return new MoleculerClientError(i18next.t("error_data_not_found"), 404);
          const filePath = this.getFilePath(file.name, uploadDir);
          if (!fs.existsSync(filePath)) return new MoleculerClientError(i18next.t("error_file_not_found"), 404);
          let stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            "Content-Type": file.mimetype,
            "Content-Length": stat.size,
            "Content-Disposition": 'attachment;filename=' + encodeURI(file.displayName || file.name)
          };
          return fs.createReadStream(filePath, {});
        } catch (e) {
          throw new MoleculerClientError(i18next.t("error_file_not_found"), 404);
        }
      }
    },

    /**
     * Get file by ID
     *
     * @actions
     * @param {String} id - File ID
     * @returns {Object} File entity
     */
    getFile: {
      rest: "GET /:id",
      auth: "required",
      params: {
        id: { type: "string" }
      },
      async handler(ctx) {
        const { id } = ctx.params;

        // Find file by ID
        const file = await this.adapter.findById(id);
        if (!file || file.isDeleted) {
          throw new MoleculerClientError(i18next.t("file_not_found") || "File not found", 404);
        }

        // Check if user has permission to access the file
        if (file.ownerId.toString() !== ctx.meta.user._id.toString() && !ctx.meta.user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t("no_permission"), 403);
        }

        return await this.transformDocuments(ctx, {}, file);
      }
    },

    /**
     * Delete file (soft delete)
     *
     * @actions
     * @param {String} id - File ID
     * @returns {Object} Result of deletion
     */
    remove: {
      rest: "DELETE /:id",
      auth: "required",
      params: {
        id: { type: "string" }
      },
      async handler(ctx) {
        const { id } = ctx.params;

        // Find file by ID
        const file = await this.adapter.findById(id);
        if (!file || file.isDeleted) {
          throw new MoleculerClientError(i18next.t("file_not_found") || "File not found", 404);
        }

        // Check if user has permission to delete the file
        if (file.ownerId.toString() !== ctx.meta.user._id.toString() && !ctx.meta.user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t("no_permission"), 403);
        }

        // Soft delete the file
        return await this.adapter.updateById(id, { isDeleted: true });
      }
    },
    createFromAudioBuffer: {
      async handler(ctx) {
        try {
          const {buffer, folder} = ctx.params;
          console.log("buffer", buffer);
          const uniqueFileName = this.createUniqueFileName('text-to-speech.mp3');
          const userId = ctx.meta.user?._id;
          const organizationId = ctx.meta.user?.organizationId;
          const filePath = this.getFilePath(uniqueFileName, this.getDirPath(folder || 'audio', uploadDir));
          await fs.writeFileSync(filePath, buffer);
          let stat = fs.statSync(filePath);

          const fileObject = {
            ownerId: userId,
            name: uniqueFileName,
            displayName: ctx.meta.displayName || uniqueFileName,
            fileType: 'audio',
            size: stat.size,
            mimetype: "audio/mpeg",
            storageType: "local_storage",
            storageLocation: folder || 'audio'
          };
          const file = await this.adapter.insert(fileObject);
          ctx.emit("fileUploaded", {file, userId, organizationId});
          return file;
        } catch (e) {
          console.log(e);
        }
      }
    },
  },

  /**
   * Events
   */
  events: {
    "user.removed": {
      async handler(ctx) {
        const userId = ctx.params.id;
        // When a user is removed, mark their files as deleted
        await this.adapter.updateMany({ ownerId: userId }, { isDeleted: true });
      }
    }
  },

  /**
   * Methods
   */
  methods: {

  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    // Create storage directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
  }
};
