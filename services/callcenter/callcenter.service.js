"use strict";

const DbMongoose = require("../../mixins/dbMongo.mixin");
const CALL_HISTORY = require("./callcenter.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const { MoleculerClientError } = require("moleculer").Errors;
const i18next = require("i18next");

module.exports = {
  name: "callcenter",
  mixins: [DbMongoose(CALL_HISTORY), BaseService, FunctionsCommon],

  /**
   * Default settings
   */
  settings: {
    /** REST Basepath */
    rest: "/callcenter",

    /** Public fields */
    fields: [
      "_id", "callerId", "receiverId", "callType", "status", "purpose",
      "callerPhone", "receiverPhone", "startedAt", "answeredAt", "endedAt",
      "duration", "summary", "notes", "tags", "conversationId", "quality",
      "customerSatisfaction", "recordingUrl", "recordingDuration", "callId",
      "createdBy", "updatedBy", "createdAt", "updatedAt"
    ],

    /** Validator schema for entity */
    entityValidator: {
      callerId: { type: "string" },
      receiverId: { type: "string" },
      callType: { type: "string", enum: ["incoming", "outgoing"] },
      status: { type: "string", enum: ["initiated", "ringing", "answered", "completed", "missed", "busy", "failed", "cancelled"], optional: true },
      purpose: { type: "string", enum: ["customer_support", "sales_inquiry", "complaint", "follow_up", "technical_support", "billing_inquiry", "other"], optional: true },
      callerPhone: { type: "string", optional: true },
      receiverPhone: { type: "string", optional: true },
      summary: { type: "string", optional: true },
      notes: { type: "string", optional: true },
      tags: { type: "array", items: "string", optional: true },
      conversationId: { type: "string", optional: true },
      quality: { type: "number", min: 1, max: 5, optional: true },
      customerSatisfaction: { type: "number", min: 1, max: 5, optional: true }
    },

    /** Populate options */
    populates: {
      callerId: {
        action: "users.get"
      },
      receiverId: {
        action: "users.get"
      },
      conversationId: {
        action: "conversations.get"
      }
    },
    populateOptions: ["callerId", "receiverId"],
  },

  /**
   * Actions
   */
  actions: {
    /**
     * Initiate a new call
     */
    initiateCall: {
      rest: "POST /initiate",
      auth: "required",
      params: {
        callerId: { type: "string" },
        receiverId: { type: "string" },
        callType: { type: "string", enum: ["incoming", "outgoing"] },
        callerPhone: { type: "string", optional: true },
        receiverPhone: { type: "string", optional: true },
        purpose: { type: "string", enum: ["customer_support", "sales_inquiry", "complaint", "follow_up", "technical_support", "billing_inquiry", "other"], optional: true },
        conversationId: { type: "string", optional: true }
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const entity = ctx.params;

        // Validate caller and receiver exist
        const [caller, receiver] = await Promise.all([
          ctx.call("users.get", { id: entity.callerId }),
          ctx.call("users.get", { id: entity.receiverId })
        ]);

        if (!caller) {
          throw new MoleculerClientError(i18next.t("Caller not found"), 404);
        }
        if (!receiver) {
          throw new MoleculerClientError(i18next.t("Receiver not found"), 404);
        }

        // Validate conversation if provided
        if (entity.conversationId) {
          const conversation = await ctx.call("conversations.get", { id: entity.conversationId });
          if (!conversation) {
            throw new MoleculerClientError(i18next.t("Conversation not found"), 404);
          }
        }

        // Generate unique call ID
        const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Create call record
        const callData = {
          callerId: entity.callerId,
          receiverId: entity.receiverId,
          callType: entity.callType,
          status: "initiated",
          purpose: entity.purpose || "customer_support",
          callerPhone: entity.callerPhone,
          receiverPhone: entity.receiverPhone,
          conversationId: entity.conversationId,
          callId: callId,
          startedAt: new Date(),
          createdBy: user._id,
          updatedBy: user._id
        };

        const result = await this.adapter.insert(callData);

        // Emit event for call initiation
        this.broker.emit('call.initiated', {
          callId: result._id,
          callerId: entity.callerId,
          receiverId: entity.receiverId,
          callType: entity.callType
        });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Answer a call
     */
    answerCall: {
      rest: "PUT /:id/answer",
      auth: "required",
      params: {
        id: "string"
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id } = ctx.params;

        // Check if call exists
        const call = await this.adapter.findById(id);
        if (!call) {
          throw new MoleculerClientError(i18next.t("Call not found"), 404);
        }

        if (call.status !== "initiated" && call.status !== "ringing") {
          throw new MoleculerClientError(i18next.t("Call cannot be answered in current status"), 400);
        }

        // Update call status
        const result = await this.adapter.updateById(id, {
          $set: {
            status: "answered",
            answeredAt: new Date(),
            updatedBy: user._id
          }
        });

        // Emit event for call answered
        this.broker.emit('call.answered', {
          callId: id,
          answeredBy: user._id,
          answeredAt: new Date()
        });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * End a call
     */
    endCall: {
      rest: "PUT /:id/end",
      auth: "required",
      params: {
        id: "string",
        summary: { type: "string", optional: true },
        notes: { type: "string", optional: true },
        quality: { type: "number", min: 1, max: 5, optional: true },
        customerSatisfaction: { type: "number", min: 1, max: 5, optional: true },
        tags: { type: "array", items: "string", optional: true }
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id, summary, notes, quality, customerSatisfaction, tags } = ctx.params;

        // Check if call exists
        const call = await this.adapter.findById(id);
        if (!call) {
          throw new MoleculerClientError(i18next.t("Call not found"), 404);
        }

        if (call.status === "completed" || call.status === "failed") {
          throw new MoleculerClientError(i18next.t("Call is already ended"), 400);
        }

        const endedAt = new Date();
        let duration = 0;

        // Calculate duration if call was answered
        if (call.answeredAt) {
          duration = Math.floor((endedAt - call.answeredAt) / 1000);
        }

        // Update call
        const updateData = {
          status: "completed",
          endedAt: endedAt,
          duration: duration,
          updatedBy: user._id
        };

        if (summary) updateData.summary = summary;
        if (notes) updateData.notes = notes;
        if (quality) updateData.quality = quality;
        if (customerSatisfaction) updateData.customerSatisfaction = customerSatisfaction;
        if (tags) updateData.tags = tags;

        const result = await this.adapter.updateById(id, { $set: updateData });

        // Emit event for call ended
        this.broker.emit('call.ended', {
          callId: id,
          endedBy: user._id,
          duration: duration,
          status: "completed"
        });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Mark call as missed
     */
    missCall: {
      rest: "PUT /:id/miss",
      auth: "required",
      params: {
        id: "string",
        reason: { type: "string", optional: true }
      },
      async handler(ctx) {
        const { user } = ctx.meta;
        const { id, reason } = ctx.params;

        // Check if call exists
        const call = await this.adapter.findById(id);
        if (!call) {
          throw new MoleculerClientError(i18next.t("Call not found"), 404);
        }

        if (call.status !== "initiated" && call.status !== "ringing") {
          throw new MoleculerClientError(i18next.t("Call cannot be marked as missed"), 400);
        }

        // Update call
        const updateData = {
          status: "missed",
          endedAt: new Date(),
          updatedBy: user._id
        };

        if (reason) updateData.notes = reason;

        const result = await this.adapter.updateById(id, { $set: updateData });

        // Emit event for missed call
        this.broker.emit('call.missed', {
          callId: id,
          callerId: call.callerId,
          receiverId: call.receiverId
        });

        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, result);
      }
    },

    /**
     * Get call statistics
     */
    getStats: {
      rest: "GET /stats",
      auth: "required",
      params: {
        startDate: { type: "string", optional: true },
        endDate: { type: "string", optional: true },
        userId: { type: "string", optional: true }
      },
      cache: {
        keys: ["startDate", "endDate", "userId"],
        ttl: 300 // 5 minutes cache
      },
      async handler(ctx) {
        const { startDate, endDate, userId } = ctx.params;
        
        // Build query
        let query = { isDeleted: false };
        
        if (startDate || endDate) {
          query.startedAt = {};
          if (startDate) query.startedAt.$gte = new Date(startDate);
          if (endDate) query.startedAt.$lte = new Date(endDate);
        }
        
        if (userId) {
          query.$or = [
            { callerId: userId },
            { receiverId: userId }
          ];
        }

        const stats = await Promise.all([
          // Total calls
          this.adapter.count({ query }),
          
          // Calls by status
          this.adapter.collection.aggregate([
            { $match: query },
            { $group: { _id: "$status", count: { $sum: 1 } } }
          ]).toArray(),
          
          // Calls by type
          this.adapter.collection.aggregate([
            { $match: query },
            { $group: { _id: "$callType", count: { $sum: 1 } } }
          ]).toArray(),
          
          // Calls by purpose
          this.adapter.collection.aggregate([
            { $match: query },
            { $group: { _id: "$purpose", count: { $sum: 1 } } }
          ]).toArray(),
          
          // Average duration
          this.adapter.collection.aggregate([
            { $match: { ...query, status: "completed", duration: { $gt: 0 } } },
            { $group: { _id: null, avgDuration: { $avg: "$duration" } } }
          ]).toArray(),
          
          // Average quality rating
          this.adapter.collection.aggregate([
            { $match: { ...query, quality: { $exists: true } } },
            { $group: { _id: null, avgQuality: { $avg: "$quality" } } }
          ]).toArray()
        ]);

        return {
          totalCalls: stats[0],
          byStatus: stats[1],
          byType: stats[2],
          byPurpose: stats[3],
          averageDuration: stats[4][0]?.avgDuration || 0,
          averageQuality: stats[5][0]?.avgQuality || 0
        };
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    /**
     * Find calls by user
     */
    async findByUser(userId, options = {}) {
      const query = {
        $or: [
          { callerId: userId },
          { receiverId: userId }
        ],
        isDeleted: false,
        ...options.query
      };

      return this.adapter.find({
        query,
        sort: options.sort || ["-startedAt"],
        limit: options.limit,
        offset: options.offset
      });
    },

    /**
     * Find calls by conversation
     */
    async findByConversation(conversationId, options = {}) {
      const query = {
        conversationId,
        isDeleted: false,
        ...options.query
      };

      return this.adapter.find({
        query,
        sort: options.sort || ["-startedAt"],
        limit: options.limit,
        offset: options.offset
      });
    }
  },

  /**
   * Events
   */
  events: {
    "user.removed": {
      async handler(ctx) {
        const userId = ctx.params.id;

        // Mark calls as deleted where user is involved
        await this.adapter.updateMany(
          {
            $or: [
              { callerId: userId },
              { receiverId: userId }
            ]
          },
          { $set: { isDeleted: true } }
        );

        this.logger.info(`User ${userId} removed from call history`);
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
    this.logger.info("Call Center service created");
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    // Create indexes if needed
    if (this.adapter.collection) {
      this.logger.info("Creating indexes for call history collection...");
      await this.adapter.collection.createIndex({ callerId: 1, startedAt: -1 });
      await this.adapter.collection.createIndex({ receiverId: 1, startedAt: -1 });
      await this.adapter.collection.createIndex({ status: 1, startedAt: -1 });
      await this.adapter.collection.createIndex({ callType: 1, startedAt: -1 });
      await this.adapter.collection.createIndex({ purpose: 1, startedAt: -1 });
      await this.adapter.collection.createIndex({ conversationId: 1, startedAt: -1 });
      await this.adapter.collection.createIndex({ callId: 1 });
      await this.adapter.collection.createIndex({ isDeleted: 1 });
    }
  }
};
