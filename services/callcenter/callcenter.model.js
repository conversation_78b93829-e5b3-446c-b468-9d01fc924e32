const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { CALL_HISTORY, USER, CONVERSATIONS } = require('../../constants/dbCollections');

const { Schema } = mongoose;

// Define call status
const callStatus = {
  INITIATED: 'initiated',
  RINGING: 'ringing',
  ANSWERED: 'answered',
  COMPLETED: 'completed',
  MISSED: 'missed',
  BUSY: 'busy',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

// Define call types
const callTypes = {
  INCOMING: 'incoming',
  OUTGOING: 'outgoing'
};

// Define call purposes
const callPurposes = {
  CUSTOMER_SUPPORT: 'customer_support',
  SALES_INQUIRY: 'sales_inquiry',
  COMPLAINT: 'complaint',
  FOLLOW_UP: 'follow_up',
  TECHNICAL_SUPPORT: 'technical_support',
  BILLING_INQUIRY: 'billing_inquiry',
  OTHER: 'other'
};

const callHistorySchema = new Schema({
  // Call participants
  callerId: {
    type: Schema.Types.ObjectId,
    ref: USER,
    required: true,
    index: true
  },
  receiverId: {
    type: Schema.Types.ObjectId,
    ref: USER,
    required: true,
    index: true
  },
  
  // Call details
  callType: {
    type: String,
    enum: Object.values(callTypes),
    required: true,
    index: true
  },
  
  status: {
    type: String,
    enum: Object.values(callStatus),
    default: callStatus.INITIATED,
    index: true
  },
  
  purpose: {
    type: String,
    enum: Object.values(callPurposes),
    default: callPurposes.CUSTOMER_SUPPORT,
    index: true
  },
  
  // Phone numbers
  callerPhone: {
    type: String,
    trim: true,
    index: true
  },
  
  receiverPhone: {
    type: String,
    trim: true,
    index: true
  },
  
  // Timing information
  startedAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  answeredAt: {
    type: Date,
    index: true
  },
  
  endedAt: {
    type: Date,
    index: true
  },
  
  duration: {
    type: Number, // Duration in seconds
    default: 0,
    index: true
  },
  
  // Call content and notes
  summary: {
    type: String,
    trim: true
  },
  
  notes: {
    type: String,
    trim: true
  },
  
  tags: [{
    type: String,
    trim: true
  }],
  
  // Related conversation
  conversationId: {
    type: Schema.Types.ObjectId,
    ref: CONVERSATIONS,
    index: true
  },
  
  // Call quality and feedback
  quality: {
    type: Number,
    min: 1,
    max: 5
  },
  
  customerSatisfaction: {
    type: Number,
    min: 1,
    max: 5
  },
  
  // Recording information
  recordingUrl: {
    type: String,
    trim: true
  },
  
  recordingDuration: {
    type: Number // Duration in seconds
  },
  
  // System information
  callId: {
    type: String,
    unique: true,
    index: true
  },
  
  systemData: {
    type: Schema.Types.Mixed // For storing system-specific call data
  },
  
  // Tracking fields
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: USER
  },
  
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: USER
  },
  
  isDeleted: {
    type: Boolean,
    default: false,
    select: false
  }
}, {
  timestamps: true,
  versionKey: false
});

// Add compound indexes for better query performance
callHistorySchema.index({ callerId: 1, startedAt: -1 });
callHistorySchema.index({ receiverId: 1, startedAt: -1 });
callHistorySchema.index({ status: 1, startedAt: -1 });
callHistorySchema.index({ callType: 1, startedAt: -1 });
callHistorySchema.index({ purpose: 1, startedAt: -1 });
callHistorySchema.index({ conversationId: 1, startedAt: -1 });
callHistorySchema.index({ callerPhone: 1 });
callHistorySchema.index({ receiverPhone: 1 });
callHistorySchema.index({ tags: 1 });

// Pre-save middleware to calculate duration
callHistorySchema.pre('save', function(next) {
  if (this.answeredAt && this.endedAt) {
    this.duration = Math.floor((this.endedAt - this.answeredAt) / 1000);
  }
  next();
});

// Pre-update middleware to calculate duration
callHistorySchema.pre(['findOneAndUpdate', 'updateOne', 'updateMany'], function(next) {
  const update = this.getUpdate();
  if (update.$set && update.$set.answeredAt && update.$set.endedAt) {
    update.$set.duration = Math.floor((update.$set.endedAt - update.$set.answeredAt) / 1000);
  }
  next();
});

callHistorySchema.plugin(mongoosePaginate);

// Export enums for use in service
callHistorySchema.statics.callStatus = callStatus;
callHistorySchema.statics.callTypes = callTypes;
callHistorySchema.statics.callPurposes = callPurposes;

module.exports = mongoose.model(CALL_HISTORY, callHistorySchema, CALL_HISTORY);
