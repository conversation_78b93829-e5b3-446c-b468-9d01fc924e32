"use strict";

const cors = require('cors');

/**
 * Create a CORS middleware
 *
 * @param {Object} opts - CORS options
 * @returns {Function} Express middleware
 */
module.exports = function createCorsMiddleware(opts = {}) {
  const defaultOptions = {
    // Allow all origins by default
    origin: (origin, callback) => {
      // Allow requests with no origin (like mobile apps, curl, etc)
      if (!origin) return callback(null, true);

      // Check if origin is in whitelist
      if (opts.whitelist && opts.whitelist.length > 0) {
        const isWhitelisted = opts.whitelist.some(domain => {
          if (domain instanceof RegExp) {
            return domain.test(origin);
          }
          return domain === origin || domain === '*';
        });

        if (isWhitelisted) {
          return callback(null, true);
        } else {
          return callback(new Error(`Origin ${origin} not allowed by CORS`), false);
        }
      }

      // If no whitelist is specified, allow all origins
      return callback(null, true);
    },
    // Allow common methods
    methods: opts.methods || 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    // Allow cookies
    credentials: opts.credentials !== undefined ? opts.credentials : true,
    // Cache preflight response for 1 hour (3600 seconds)
    maxAge: opts.maxAge || 3600,
    // Allow common headers
    allowedHeaders: opts.allowedHeaders || 'Content-Type,Authorization,X-Requested-With',
    // Expose these headers
    exposedHeaders: opts.exposedHeaders || 'Content-Range,X-Content-Range',
    // Pass the CORS preflight response to the next handler
    preflightContinue: opts.preflightContinue || false,
    // Enable CORS preflight
    optionsSuccessStatus: opts.optionsSuccessStatus || 204,
  };

  // Create and return the CORS middleware
  return cors(defaultOptions);
};
