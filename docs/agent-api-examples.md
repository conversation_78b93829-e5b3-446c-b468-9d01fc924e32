# 🚀 Agent API - <PERSON><PERSON> dụ thực tế

## 📋 Mục lục
1. [Setup và Authentication](#setup-và-authentication)
2. [<PERSON><PERSON> dụ cơ bản](#ví-dụ-cơ-bản)
3. [<PERSON><PERSON> dụ nâng cao](#ví-dụ-nâng-cao)
4. [Integration với Frontend](#integration-với-frontend)
5. [<PERSON><PERSON><PERSON>ling](#error-handling)

---

## Setup và Authentication

### 1. C<PERSON><PERSON> hình base
```javascript
const AGENT_API_BASE = 'http://localhost:3000/api/langchain/agent';
const AUTH_TOKEN = 'your_jwt_token_here';

const apiHeaders = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${AUTH_TOKEN}`
};
```

### 2. Helper function
```javascript
const callAgentAPI = async (endpoint, method = 'GET', body = null) => {
  const config = {
    method,
    headers: apiHeaders
  };
  
  if (body) {
    config.body = JSON.stringify(body);
  }
  
  const response = await fetch(`${AGENT_API_BASE}${endpoint}`, config);
  
  if (!response.ok) {
    throw new Error(`API Error: ${response.status} - ${response.statusText}`);
  }
  
  return await response.json();
};
```

---

## Ví dụ cơ bản

### 1. 🛍️ Shop hỏi về hàng bị mất
```javascript
const handleLostPackageQuestion = async () => {
  try {
    const result = await callAgentAPI('/ask', 'POST', {
      question: "Khách hàng báo không nhận được hàng, tôi phải làm gì?",
      userId: "shop_12345",
      userType: "shop"
    });
    
    console.log("🤖 AI Answer:", result.answer);
    console.log("📊 Confidence:", result.confidence);
    console.log("💡 Recommendations:", result.businessLogicResult?.recommendations);
    
    return result;
  } catch (error) {
    console.error("❌ Error:", error);
  }
};

// Sử dụng
handleLostPackageQuestion();
```

**Expected Output:**
```json
{
  "answer": "Khi khách hàng báo không nhận được hàng, bạn cần thực hiện các bước sau:\n\n1. **Kiểm tra thông tin đơn hàng**: Xác minh mã vận đơn và trạng thái giao hàng\n2. **Liên hệ đơn vị vận chuyển**: Gọi hotline để tra cứu chi tiết\n3. **Thông báo khách hàng**: Cập nhật tình hình và thời gian xử lý\n4. **Chuẩn bị bồi thường**: Nếu xác nhận hàng bị mất\n\nBạn có thể liên hệ bộ phận CSKH để được hỗ trợ thêm.",
  "confidence": 0.89,
  "relatedBusinessRules": [...],
  "conversationId": "conv_abc123",
  "businessLogicResult": {
    "recommendations": [
      "Kiểm tra thông tin đơn hàng và mã vận đơn",
      "Liên hệ đơn vị vận chuyển để tra cứu",
      "Thông báo cho khách hàng về tình trạng đơn hàng"
    ]
  }
}
```

### 2. 💰 CSKH hỏi về chính sách COD
```javascript
const handleCODPolicyQuestion = async () => {
  try {
    const result = await callAgentAPI('/ask', 'POST', {
      question: "Khách hàng muốn thay đổi số tiền COD, có được không?",
      userId: "cskh_67890", 
      userType: "cskh",
      confidenceThreshold: 0.8
    });
    
    if (result.confidence >= 0.8) {
      console.log("✅ High confidence answer:", result.answer);
    } else {
      console.log("⚠️ Low confidence, manual review needed");
    }
    
    return result;
  } catch (error) {
    console.error("❌ Error:", error);
  }
};
```

### 3. 📞 Tiếp tục cuộc hội thoại
```javascript
const continueConversation = async (conversationId) => {
  try {
    const result = await callAgentAPI('/ask', 'POST', {
      question: "Vậy thời gian xử lý bao lâu?",
      conversationId: conversationId,
      userId: "shop_12345",
      userType: "shop"
    });
    
    console.log("🔄 Follow-up answer:", result.answer);
    return result;
  } catch (error) {
    console.error("❌ Error:", error);
  }
};
```

---

## Ví dụ nâng cao

### 1. 🎯 Chatbot tích hợp
```javascript
class AgentChatbot {
  constructor(userId, userType) {
    this.userId = userId;
    this.userType = userType;
    this.conversationId = null;
    this.chatHistory = [];
  }
  
  async ask(question) {
    try {
      const payload = {
        question,
        userId: this.userId,
        userType: this.userType
      };
      
      // Thêm conversationId nếu có
      if (this.conversationId) {
        payload.conversationId = this.conversationId;
      }
      
      const result = await callAgentAPI('/ask', 'POST', payload);
      
      // Lưu conversationId cho lần sau
      if (!this.conversationId) {
        this.conversationId = result.conversationId;
      }
      
      // Lưu lịch sử chat
      this.chatHistory.push({
        question,
        answer: result.answer,
        confidence: result.confidence,
        timestamp: new Date()
      });
      
      return result;
    } catch (error) {
      console.error("Chatbot error:", error);
      return {
        answer: "Xin lỗi, tôi không thể trả lời câu hỏi này. Vui lòng liên hệ nhân viên hỗ trợ.",
        confidence: 0,
        error: true
      };
    }
  }
  
  async getHistory() {
    if (!this.conversationId) return [];
    
    try {
      return await callAgentAPI(`/conversation/${this.conversationId}/history`);
    } catch (error) {
      console.error("Error getting history:", error);
      return this.chatHistory; // Fallback to local history
    }
  }
  
  getStats() {
    return {
      totalQuestions: this.chatHistory.length,
      averageConfidence: this.chatHistory.reduce((sum, item) => sum + item.confidence, 0) / this.chatHistory.length,
      conversationId: this.conversationId
    };
  }
}

// Sử dụng
const shopBot = new AgentChatbot("shop_12345", "shop");

const demo = async () => {
  // Câu hỏi đầu tiên
  let result1 = await shopBot.ask("Hàng của khách bị mất, tôi phải làm gì?");
  console.log("Answer 1:", result1.answer);
  
  // Câu hỏi tiếp theo
  let result2 = await shopBot.ask("Thời gian xử lý bao lâu?");
  console.log("Answer 2:", result2.answer);
  
  // Xem thống kê
  console.log("Stats:", shopBot.getStats());
  
  // Xem lịch sử
  const history = await shopBot.getHistory();
  console.log("History:", history);
};

demo();
```

### 2. 🔄 Batch processing
```javascript
const processBatchQuestions = async (questions, userId, userType) => {
  const results = [];
  
  for (const question of questions) {
    try {
      const result = await callAgentAPI('/ask', 'POST', {
        question,
        userId,
        userType
      });
      
      results.push({
        question,
        answer: result.answer,
        confidence: result.confidence,
        success: true
      });
      
      // Delay để tránh rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      results.push({
        question,
        error: error.message,
        success: false
      });
    }
  }
  
  return results;
};

// Sử dụng
const questions = [
  "Làm sao để thay đổi địa chỉ giao hàng?",
  "Chính sách hoàn tiền như thế nào?",
  "Thời gian giao hàng bao lâu?"
];

processBatchQuestions(questions, "shop_123", "shop")
  .then(results => {
    console.log("Batch results:", results);
    
    // Thống kê
    const successful = results.filter(r => r.success).length;
    const avgConfidence = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + r.confidence, 0) / successful;
    
    console.log(`✅ ${successful}/${results.length} successful`);
    console.log(`📊 Average confidence: ${avgConfidence.toFixed(2)}`);
  });
```

### 3. 🎨 Smart response formatting
```javascript
const formatAgentResponse = (result) => {
  const { answer, confidence, relatedBusinessRules, businessLogicResult } = result;
  
  let formattedResponse = {
    mainAnswer: answer,
    confidenceLevel: getConfidenceLevel(confidence),
    recommendations: [],
    relatedTopics: [],
    needsHumanReview: confidence < 0.7
  };
  
  // Thêm recommendations từ business logic
  if (businessLogicResult?.recommendations) {
    formattedResponse.recommendations = businessLogicResult.recommendations;
  }
  
  // Thêm related topics
  if (relatedBusinessRules?.length > 0) {
    formattedResponse.relatedTopics = relatedBusinessRules.map(rule => ({
      category: rule.category,
      keywords: rule.keywords,
      similarity: rule.similarityScore
    }));
  }
  
  return formattedResponse;
};

const getConfidenceLevel = (confidence) => {
  if (confidence >= 0.8) return "HIGH";
  if (confidence >= 0.6) return "MEDIUM";
  if (confidence >= 0.4) return "LOW";
  return "VERY_LOW";
};

// Sử dụng
const askWithFormatting = async (question, userId, userType) => {
  try {
    const rawResult = await callAgentAPI('/ask', 'POST', {
      question, userId, userType
    });
    
    const formatted = formatAgentResponse(rawResult);
    
    console.log("🤖 Main Answer:", formatted.mainAnswer);
    console.log("📊 Confidence:", formatted.confidenceLevel);
    
    if (formatted.recommendations.length > 0) {
      console.log("💡 Recommendations:");
      formatted.recommendations.forEach((rec, i) => {
        console.log(`   ${i + 1}. ${rec}`);
      });
    }
    
    if (formatted.needsHumanReview) {
      console.log("⚠️ Needs human review");
    }
    
    return formatted;
  } catch (error) {
    console.error("❌ Error:", error);
  }
};
```

---

## Integration với Frontend

### 1. 🌐 React Hook
```javascript
import { useState, useCallback } from 'react';

const useAgentChat = (userId, userType) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [conversationId, setConversationId] = useState(null);
  
  const askAgent = useCallback(async (question) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/langchain/agent/ask', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          question,
          userId,
          userType,
          conversationId
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!conversationId) {
        setConversationId(result.conversationId);
      }
      
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [userId, userType, conversationId]);
  
  return { askAgent, loading, error, conversationId };
};

// Component sử dụng
const ChatComponent = ({ userId, userType }) => {
  const { askAgent, loading, error } = useAgentChat(userId, userType);
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim()) return;
    
    // Thêm câu hỏi của user
    setMessages(prev => [...prev, { type: 'user', text: input }]);
    
    try {
      const result = await askAgent(input);
      
      // Thêm câu trả lời của AI
      setMessages(prev => [...prev, { 
        type: 'ai', 
        text: result.answer,
        confidence: result.confidence
      }]);
      
    } catch (err) {
      setMessages(prev => [...prev, { 
        type: 'error', 
        text: 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.' 
      }]);
    }
    
    setInput('');
  };
  
  return (
    <div className="chat-container">
      <div className="messages">
        {messages.map((msg, index) => (
          <div key={index} className={`message ${msg.type}`}>
            {msg.text}
            {msg.confidence && (
              <span className="confidence">
                Confidence: {(msg.confidence * 100).toFixed(0)}%
              </span>
            )}
          </div>
        ))}
      </div>
      
      <form onSubmit={handleSubmit}>
        <input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Nhập câu hỏi..."
          disabled={loading}
        />
        <button type="submit" disabled={loading}>
          {loading ? 'Đang xử lý...' : 'Gửi'}
        </button>
      </form>
      
      {error && <div className="error">Lỗi: {error}</div>}
    </div>
  );
};
```

### 2. 📱 Vue.js Integration
```javascript
// composables/useAgent.js
import { ref, reactive } from 'vue';

export function useAgent(userId, userType) {
  const loading = ref(false);
  const error = ref(null);
  const conversationId = ref(null);
  
  const askAgent = async (question) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await $fetch('/api/langchain/agent/ask', {
        method: 'POST',
        body: {
          question,
          userId,
          userType,
          conversationId: conversationId.value
        }
      });
      
      if (!conversationId.value) {
        conversationId.value = response.conversationId;
      }
      
      return response;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  return {
    askAgent,
    loading: readonly(loading),
    error: readonly(error),
    conversationId: readonly(conversationId)
  };
}
```

---

## Error Handling

### 1. 🛡️ Comprehensive error handling
```javascript
const robustAgentCall = async (question, userId, userType, retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const result = await callAgentAPI('/ask', 'POST', {
        question, userId, userType
      });
      
      // Validate response
      if (!result.answer) {
        throw new Error('Invalid response: missing answer');
      }
      
      return result;
      
    } catch (error) {
      console.error(`Attempt ${attempt} failed:`, error);
      
      if (attempt === retries) {
        // Final attempt failed
        return {
          answer: "Xin lỗi, hệ thống đang gặp sự cố. Vui lòng liên hệ nhân viên hỗ trợ hoặc thử lại sau.",
          confidence: 0,
          error: true,
          originalError: error.message
        };
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
};
```

### 2. 📊 Monitoring và logging
```javascript
const monitoredAgentCall = async (question, userId, userType) => {
  const startTime = Date.now();
  
  try {
    const result = await callAgentAPI('/ask', 'POST', {
      question, userId, userType
    });
    
    // Log successful call
    console.log('Agent call successful:', {
      userId,
      userType,
      confidence: result.confidence,
      processingTime: Date.now() - startTime,
      questionLength: question.length
    });
    
    return result;
    
  } catch (error) {
    // Log error
    console.error('Agent call failed:', {
      userId,
      userType,
      error: error.message,
      processingTime: Date.now() - startTime,
      questionLength: question.length
    });
    
    throw error;
  }
};
```

---

## 🎯 Tips & Best Practices

1. **Luôn kiểm tra confidence score**
2. **Implement retry logic cho network errors**
3. **Cache responses cho câu hỏi phổ biến**
4. **Monitor performance và error rates**
5. **Provide fallback responses**
6. **Validate input trước khi gửi**
7. **Handle timeout gracefully**
8. **Log interactions cho debugging**
