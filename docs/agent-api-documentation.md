# 🤖 Agent Service API Documentation

## Tổng quan

Service Agent là hệ thống AI tự động xử lý câu hỏi và cung cấp câu trả lời thông minh cho hệ thống chăm sóc khách hàng GHVN. Service này tích hợp v<PERSON><PERSON>, OpenAI GPT và hệ thống nghiệp vụ để cung cấp trải nghiệm hỗ trợ tự động.

## Base URL
```
http://localhost:3000/api/langchain/agent
```

## Authentication
Tất cả các endpoint đều yêu cầu authentication token trong header:
```
Authorization: Bearer <your_jwt_token>
```

---

## 📋 API Endpoints

### 1. 🎯 Hỏi đáp tự động - `/ask`

**Endpoint chính để xử lý câu hỏi và trả lời tự động**

#### Request
```http
POST /api/langchain/agent/ask
Content-Type: application/json
Authorization: Bearer <token>

{
  "question": "Hàng của tôi bị mất, tôi phải làm gì?",
  "conversationId": "optional_conversation_id",
  "userId": "user_id_here",
  "userType": "shop",
  "confidenceThreshold": 0.7,
  "includeBusinessLogic": true
}
```

#### Parameters
| Tham số | Loại | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `question` | string | ✅ | Câu hỏi của người dùng (1-2000 ký tự) |
| `conversationId` | string | ❌ | ID cuộc hội thoại (tạo mới nếu không có) |
| `userId` | string | ✅ | ID người dùng |
| `userType` | string | ✅ | Loại người dùng: `"shop"` hoặc `"cskh"` |
| `confidenceThreshold` | number | ❌ | Ngưỡng độ tin cậy (0-1, mặc định: 0.7) |
| `includeBusinessLogic` | boolean | ❌ | Áp dụng logic nghiệp vụ (mặc định: true) |

#### Response
```json
{
  "answer": "Để xử lý trường hợp hàng bị mất, bạn cần thực hiện các bước sau:\n1. Kiểm tra thông tin đơn hàng và mã vận đơn\n2. Liên hệ đơn vị vận chuyển để tra cứu\n3. Chuẩn bị thông tin bồi thường nếu cần thiết...",
  "confidence": 0.85,
  "relatedBusinessRules": [
    {
      "id": "business_rule_id",
      "category": "lost_package",
      "keywords": ["mất", "thất lạc", "tráo hàng"],
      "similarityScore": 0.92,
      "response": "Quy trình xử lý hàng mất..."
    }
  ],
  "conversationId": "conversation_id",
  "messageId": "message_id",
  "businessLogicResult": {
    "appliedRule": "lost_package",
    "confidence": 0.92,
    "recommendations": [
      "Kiểm tra thông tin đơn hàng và mã vận đơn",
      "Liên hệ đơn vị vận chuyển để tra cứu",
      "Chuẩn bị thông tin bồi thường nếu cần thiết"
    ]
  },
  "metadata": {
    "questionEmbedding": [0.1, -0.2, 0.3, 0.4, -0.1],
    "processingTime": 1250,
    "model": "gpt-3.5-turbo-1106",
    "userType": "shop"
  }
}
```

#### Response Fields
| Trường | Loại | Mô tả |
|--------|------|-------|
| `answer` | string | Câu trả lời từ AI |
| `confidence` | number | Độ tin cậy tổng thể (0-1) |
| `relatedBusinessRules` | array | Danh sách quy tắc nghiệp vụ liên quan |
| `conversationId` | string | ID cuộc hội thoại |
| `messageId` | string | ID tin nhắn phản hồi |
| `businessLogicResult` | object | Kết quả áp dụng logic nghiệp vụ |
| `metadata` | object | Thông tin metadata về quá trình xử lý |

---

### 2. 📜 Lịch sử hội thoại - `/conversation/{conversationId}/history`

**Lấy lịch sử hội thoại với AI**

#### Request
```http
GET /api/langchain/agent/conversation/60f7b3b3b3b3b3b3b3b3b3b3/history?limit=50
Authorization: Bearer <token>
```

#### Parameters
| Tham số | Loại | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `conversationId` | string | ✅ | ID cuộc hội thoại (trong URL) |
| `limit` | number | ❌ | Số lượng tin nhắn tối đa (mặc định: 50) |

#### Response
```json
{
  "conversationId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "messages": [
    {
      "id": "message_id_1",
      "text": "Hàng của tôi bị mất, tôi phải làm gì?",
      "senderRole": "shop",
      "sentAt": "2024-01-15T10:30:00Z",
      "isAI": false
    },
    {
      "id": "message_id_2", 
      "text": "Để xử lý trường hợp hàng bị mất...",
      "senderRole": "support",
      "sentAt": "2024-01-15T10:30:15Z",
      "isAI": true
    }
  ],
  "total": 2
}
```

---

### 3. 📊 Thống kê Agent - `/stats`

**Lấy thống kê hoạt động của Agent**

#### Request
```http
GET /api/langchain/agent/stats
Authorization: Bearer <token>
```

#### Response
```json
{
  "totalAIConversations": 150,
  "businessRulesCount": 25,
  "activeBusinessRules": 20,
  "rulesWithEmbeddings": 18,
  "lastUpdated": "2024-01-15T10:30:00Z"
}
```

---

## 🔧 Cách sử dụng

### 1. Tích hợp cơ bản

```javascript
// Gửi câu hỏi đến Agent
const askAgent = async (question, userId, userType) => {
  const response = await fetch('/api/langchain/agent/ask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      question,
      userId,
      userType
    })
  });
  
  return await response.json();
};

// Sử dụng
const result = await askAgent(
  "Làm sao để thay đổi COD của đơn hàng?", 
  "user123", 
  "shop"
);
console.log(result.answer);
```

### 2. Tích hợp với cuộc hội thoại có sẵn

```javascript
const continueConversation = async (question, conversationId, userId, userType) => {
  const response = await fetch('/api/langchain/agent/ask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      question,
      conversationId,
      userId,
      userType
    })
  });
  
  return await response.json();
};
```

### 3. Tùy chỉnh độ tin cậy

```javascript
const askWithCustomThreshold = async (question, userId, userType) => {
  const response = await fetch('/api/langchain/agent/ask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      question,
      userId,
      userType,
      confidenceThreshold: 0.8, // Yêu cầu độ tin cậy cao hơn
      includeBusinessLogic: true
    })
  });
  
  return await response.json();
};
```

---

## 🎯 Các trường hợp sử dụng

### 1. Hỗ trợ Shop/Đối tác
```javascript
// Shop hỏi về quy trình xử lý đơn hàng
const shopQuestion = await askAgent(
  "Khách hàng phản ánh hàng bị lỗi, tôi cần làm gì?",
  "shop_id_123",
  "shop"
);
```

### 2. Hỗ trợ nhân viên CSKH
```javascript
// CSKH hỏi về chính sách bồi thường
const cskhQuestion = await askAgent(
  "Chính sách bồi thường cho hàng bị mất như thế nào?",
  "cskh_id_456", 
  "cskh"
);
```

### 3. Xử lý câu hỏi phức tạp
```javascript
// Câu hỏi yêu cầu logic nghiệp vụ phức tạp
const complexQuestion = await askAgent(
  "Khách hàng muốn đổi địa chỉ giao hàng và tăng COD, có được không?",
  "user_789",
  "shop"
);

// Kiểm tra kết quả logic nghiệp vụ
if (complexQuestion.businessLogicResult) {
  console.log("Khuyến nghị:", complexQuestion.businessLogicResult.recommendations);
}
```

---

## ⚠️ Lưu ý quan trọng

### 1. Rate Limiting
- Mỗi request có timeout 2 phút
- Khuyến nghị không gửi quá 10 requests/phút cho mỗi user

### 2. Độ tin cậy
- `confidence < 0.5`: Độ tin cậy thấp, nên chuyển cho nhân viên
- `confidence 0.5-0.7`: Độ tin cậy trung bình, có thể sử dụng với thận trọng  
- `confidence > 0.7`: Độ tin cậy cao, có thể tin tưởng sử dụng

### 3. Error Handling
```javascript
try {
  const result = await askAgent(question, userId, userType);
  
  if (result.confidence < 0.5) {
    // Chuyển cho nhân viên xử lý
    console.log("Độ tin cậy thấp, cần can thiệp thủ công");
  }
} catch (error) {
  console.error("Lỗi khi gọi Agent:", error);
  // Fallback: chuyển cho nhân viên hoặc hiển thị thông báo lỗi
}
```

### 4. Best Practices
- Luôn kiểm tra `confidence` score trước khi sử dụng kết quả
- Lưu `conversationId` để duy trì ngữ cảnh hội thoại
- Sử dụng `businessLogicResult` để có thêm thông tin xử lý
- Monitor `metadata.processingTime` để tối ưu performance

---

## 🔍 Troubleshooting

### Lỗi thường gặp

1. **401 Unauthorized**: Kiểm tra JWT token
2. **400 Bad Request**: Kiểm tra format request body
3. **500 Internal Server Error**: Lỗi hệ thống, thử lại sau
4. **Timeout**: Request quá lâu, kiểm tra kết nối mạng

### Debug
Sử dụng `metadata` trong response để debug:
```javascript
console.log("Processing time:", result.metadata.processingTime);
console.log("Model used:", result.metadata.model);
console.log("Question embedding:", result.metadata.questionEmbedding);
```
