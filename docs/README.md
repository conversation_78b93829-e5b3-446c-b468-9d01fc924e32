# 🤖 GHVN AI Agent System

## 📋 Tổng quan hệ thống

Hệ thống AI Agent GHVN là giải pháp tự động hóa chăm sóc khách hàng thông minh, tích hợ<PERSON>, OpenAI GPT và vector similarity search để cung cấp câu trả lời chính xác và hữu ích.

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   API Gateway   │───▶│  Agent Service  │
│  (Shop/CSKH)    │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │  LangChain      │◀────────────┘
                       │  Service        │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │  Business       │
                       │  Service        │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   MongoDB       │
                       │  (Embeddings)   │
                       └─────────────────┘
```

## 🔧 Các thành phần chính

### 1. 🎯 Agent Service (`services/langchain/agent.service.js`)
- **Chức năng**: Xử lý câu hỏi và trả lời tự động
- **Pipeline**: Question → Embedding → Similarity Search → Business Logic → LLM → Response
- **API Endpoint**: `POST /api/langchain/agent/ask`

### 2. 🏢 Business Service (`services/business/`)
- **Chức năng**: <PERSON><PERSON><PERSON>n lý quy tắc nghiệp vụ và knowledge base
- **Features**: CRUD operations, Vector search, Import/Export
- **API Endpoints**: `/api/business/*`

### 3. 🧠 LangChain Service (`services/langchain/langchain.service.js`)
- **Chức năng**: Tích hợp OpenAI, tạo embeddings, chat completion
- **Features**: Text-to-speech, Embedding generation, Similarity calculation

### 4. 💬 Conversations & Messages Services
- **Chức năng**: Quản lý cuộc hội thoại và tin nhắn
- **Features**: Lưu trữ lịch sử chat, tracking user interactions

## 🚀 Quick Start

### 1. Cài đặt dependencies
```bash
npm install
```

### 2. Cấu hình environment
```bash
# .env
MONGO_URI=mongodb://localhost:27017/ghvn-cskh
OPENAI_API_KEY=your_openai_api_key
```

### 3. Khởi động services
```bash
npm run dev
```

### 4. Test API
```bash
curl -X POST http://localhost:3000/api/langchain/agent/ask \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "question": "Hàng của tôi bị mất, tôi phải làm gì?",
    "userId": "user123",
    "userType": "shop"
  }'
```

## 📚 Tài liệu API

### 📖 Tài liệu chính
- [**Agent API Documentation**](./agent-api-documentation.md) - Tài liệu API chi tiết
- [**Agent API Examples**](./agent-api-examples.md) - Ví dụ thực tế và integration

### 🎯 Endpoints chính

| Endpoint | Method | Mô tả |
|----------|--------|-------|
| `/api/langchain/agent/ask` | POST | Hỏi đáp tự động |
| `/api/langchain/agent/conversation/{id}/history` | GET | Lịch sử hội thoại |
| `/api/langchain/agent/stats` | GET | Thống kê agent |
| `/api/business/search` | POST | Tìm kiếm nghiệp vụ |
| `/api/business/import` | POST | Import dữ liệu |
| `/api/business/export` | GET | Export dữ liệu |

## 🔄 Workflow xử lý câu hỏi

```mermaid
graph TD
    A[User Question] --> B[Create Embedding]
    B --> C[Search Business Rules]
    C --> D[Apply Business Logic]
    D --> E[Generate LLM Response]
    E --> F[Save to Conversation]
    F --> G[Return Response]
    
    C --> H{Found Rules?}
    H -->|Yes| I[High Confidence]
    H -->|No| J[Low Confidence]
    
    I --> E
    J --> K[Fallback Response]
    K --> F
```

## 📊 Cấu trúc dữ liệu

### Business Rule Schema
```javascript
{
  category: "lost_package",
  keywords: ["mất", "thất lạc", "tráo hàng"],
  examples: ["Hàng của tôi bị mất", "Không nhận được hàng"],
  response: "Quy trình xử lý hàng mất...",
  embedding: [0.1, -0.2, 0.3, ...], // Vector 1536 dimensions
  status: "active",
  priority: 1,
  tags: ["urgent", "logistics"]
}
```

### Agent Response Schema
```javascript
{
  answer: "Câu trả lời từ AI",
  confidence: 0.85,
  relatedBusinessRules: [...],
  conversationId: "conv_123",
  messageId: "msg_456",
  businessLogicResult: {
    appliedRule: "lost_package",
    recommendations: [...]
  }
}
```

## 🎛️ Cấu hình

### Agent Settings
```javascript
// services/langchain/agent.service.js
settings: {
  defaultConfidenceThreshold: 0.7,
  defaultSearchLimit: 5,
  defaultModel: "gpt-3.5-turbo-1106",
  defaultTemperature: 0.3
}
```

### Business Logic Categories
- `lost_package` - Hàng bị mất/thất lạc
- `delivery_reminder` - Nhắc nhở giao hàng  
- `cod_modification` - Sửa đổi COD
- `return_request` - Yêu cầu trả hàng
- `complaint` - Khiếu nại
- `inquiry` - Tư vấn chung

## 🔍 Monitoring & Analytics

### Metrics quan trọng
- **Response Time**: Thời gian xử lý câu hỏi
- **Confidence Score**: Độ tin cậy câu trả lời
- **Business Rule Coverage**: Tỷ lệ câu hỏi match với rules
- **User Satisfaction**: Feedback từ người dùng

### Logging
```javascript
// Tự động log trong agent service
{
  userId: "user123",
  userType: "shop", 
  question: "...",
  confidence: 0.85,
  processingTime: 1250,
  businessRulesFound: 3,
  timestamp: "2024-01-15T10:30:00Z"
}
```

## 🛠️ Development

### Thêm Business Rule mới
```javascript
// 1. Tạo rule qua API
POST /api/business
{
  "category": "new_category",
  "keywords": ["keyword1", "keyword2"],
  "response": "Template response...",
  "examples": ["Example question"]
}

// 2. Hoặc import từ JSON
POST /api/business/import
{
  "data": [...]
}
```

### Tùy chỉnh Business Logic
```javascript
// services/langchain/agent.service.js
async applyBusinessLogic(ctx, { question, relatedRules, userType }) {
  switch (topRule.category) {
    case "your_new_category":
      return await this.handleYourNewLogic(ctx, { question, userType });
    // ...
  }
}
```

### Testing
```bash
# Unit tests
npm test

# Integration tests
npm run test:integration

# Load testing
npm run test:load
```

## 🚨 Troubleshooting

### Lỗi thường gặp

1. **Low Confidence Responses**
   - Kiểm tra business rules coverage
   - Cập nhật embeddings
   - Tăng số lượng examples

2. **Slow Response Time**
   - Optimize embedding search
   - Cache frequent queries
   - Reduce LLM temperature

3. **MongoDB Connection Issues**
   - Kiểm tra connection string
   - Verify network connectivity
   - Check MongoDB service status

### Debug Commands
```bash
# Check service status
curl http://localhost:3000/api/langchain/agent/stats

# Test embedding generation
curl -X POST http://localhost:3000/api/langchain/getEmbedding \
  -d '{"textContent": "test"}'

# Search business rules
curl -X POST http://localhost:3000/api/business/search \
  -d '{"query": "test", "limit": 5}'
```

## 🔮 Roadmap

### Phase 1 (Current)
- ✅ Basic Q&A functionality
- ✅ Business rules management
- ✅ Vector similarity search
- ✅ LangChain integration

### Phase 2 (Next)
- 🔄 LangGraph complex workflows
- 🔄 Multi-language support
- 🔄 Advanced analytics dashboard
- 🔄 A/B testing framework

### Phase 3 (Future)
- 📋 Voice interaction
- 📋 Image/document processing
- 📋 Predictive analytics
- 📋 Auto-learning from feedback

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Implement changes
4. Add tests
5. Update documentation
6. Submit pull request

## 📞 Support

- **Technical Issues**: Create GitHub issue
- **API Questions**: Check documentation
- **Business Logic**: Contact product team

---

**Made with ❤️ by GHVN Development Team**
