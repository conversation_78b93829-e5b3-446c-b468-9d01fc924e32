# Hướng dẫn sử dụng API Upload File

## Tổng quan

API upload file cho phép người dùng tải lên các tệp tin vào hệ thống. Các tệp tin được lưu trữ trong thư mục `storage` và thông tin về tệp tin được lưu trong cơ sở dữ liệu.

## Yêu cầu

- Người dùng phải đã đăng nhập và có token xác thực hợp lệ
- Kích thước tệp tin không vượt quá giới hạn của hệ thống

## Endpoint

```
POST /upload/file
```

## Headers

| Tên | Giá trị | Mô tả |
|-----|---------|-------|
| Authorization | Bearer {token} | Token JWT xác thực người dùng |

## Body

Sử dụng `multipart/form-data` với trường `file` ch<PERSON><PERSON> tệp tin cần upload.

## Ví dụ

### Sử dụng cURL

```bash
curl -X POST \
  http://localhost:3000/upload/file \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -F 'file=@/đường/dẫn/đến/tệp/tin.jpg'
```

### Sử dụng JavaScript (Fetch API)

```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('http://localhost:3000/upload/file', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  },
  body: formData
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

## Phản hồi

### Thành công (200 OK)

```json
{
  "_id": "60d21b4667d0d8992e610c85",
  "ownerId": "60d21b4667d0d8992e610c84",
  "name": "1624291142-file.jpg",
  "displayName": "file.jpg",
  "fileType": "image",
  "mimetype": "image/jpeg",
  "size": "12345",
  "storageType": "local_storage",
  "storageLocation": "storage",
  "createdAt": "2021-06-21T12:45:42.000Z",
  "updatedAt": "2021-06-21T12:45:42.000Z"
}
```

### Lỗi

#### Không có quyền truy cập (403 Forbidden)

```json
{
  "name": "MoleculerClientError",
  "message": "Không có quyền truy cập",
  "code": 403
}
```

#### Thiếu tệp tin (400 Bad Request)

```json
{
  "name": "MoleculerClientError",
  "message": "Thiếu tệp tin",
  "code": 400
}
```

#### Lỗi upload (500 Internal Server Error)

```json
{
  "name": "MoleculerClientError",
  "message": "Upload tệp tin thất bại",
  "code": 500,
  "type": "FILE_UPLOAD_ERROR"
}
```

## Lấy thông tin tệp tin

Sau khi upload thành công, bạn có thể lấy thông tin tệp tin bằng cách sử dụng API:

```
GET /files/{id}
```

Trong đó `{id}` là ID của tệp tin được trả về sau khi upload thành công.

## Lưu ý

- Hệ thống tự động phân loại tệp tin dựa trên MIME type (image, audio, video, file)
- Tệp tin được lưu trữ trong thư mục `storage` với tên duy nhất để tránh xung đột
- Chỉ người dùng đã tạo tệp tin hoặc quản trị viên hệ thống mới có thể truy cập thông tin tệp tin